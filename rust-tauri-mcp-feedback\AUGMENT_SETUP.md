# Augment MCP配置指南

## 📋 配置方法

### 方法1：使用Augment设置面板（推荐）

1. 打开Augment面板右上角的选项菜单
2. 点击"Settings"选项
3. 在MCP服务器部分填写：
   - **Name**: `feedback`
   - **Command**: `F:\mcp\Rustmcp\rust-tauri-mcp-feedback\src-tauri\target\release\mcp_server.exe`
   - **Args**: 留空
   - **Environment Variables**:
     - `RUST_LOG=info`
     - `MCP_MODE=server`

### 方法2：编辑settings.json

1. 按 `Cmd/Ctrl + Shift + P` 或点击Augment面板的汉堡菜单
2. 选择"Edit Settings"
3. 在Advanced下，点击"Edit in settings.json"
4. 添加以下配置：

```json
{
  "augment.advanced": {
    "mcpServers": [
      {
        "name": "feedback",
        "command": "F:\\mcp\\Rustmcp\\rust-tauri-mcp-feedback\\src-tauri\\target\\release\\mcp_server.exe",
        "args": [],
        "env": {
          "RUST_LOG": "info",
          "MCP_MODE": "server"
        }
      }
    ]
  }
}
```

## 🔧 配置说明

### 参数解释

- **name**: MCP服务器的唯一标识符，在Augment中显示为"feedback"
- **command**: MCP服务器可执行文件的完整路径
- **args**: 命令行参数（我们的服务器不需要额外参数）
- **env**: 环境变量
  - `RUST_LOG=info`: 设置日志级别
  - `MCP_MODE=server`: 标识运行模式

### 路径配置

请根据您的实际安装路径修改command字段：

```json
// Windows示例
"command": "C:\\Users\\<USER>\\mcp-feedback\\src-tauri\\target\\release\\mcp_server.exe"

// 相对路径示例（如果在PATH中）
"command": "mcp_server.exe"
```

## 🚀 使用示例

配置完成后，重启编辑器，然后在Augment中可以这样使用：

### 基本使用
```
请使用feedback工具显示以下内容并收集反馈：

## 今日工作汇报
- 完成了功能A的开发
- 修复了Bug B
- 优化了性能C

请收集用户反馈。
```

### 高级使用
```
使用feedback工具，参数如下：
- work_summary: "## 项目进展\n\n### 已完成\n- 任务1\n- 任务2\n\n### 待完成\n- 任务3"
- title: "周报反馈"
- allow_save: true
- timeout: 600
```

## 🔍 故障排除

### 常见问题

1. **服务器无法启动**
   - 检查可执行文件路径是否正确
   - 确保文件有执行权限
   - 查看Augment的错误日志

2. **工具不可用**
   - 重启编辑器
   - 检查JSON语法是否正确
   - 确认服务器配置在正确的位置

3. **中文显示问题**
   - 确保终端支持UTF-8编码
   - 检查环境变量设置

### 测试配置

可以使用我们提供的测试脚本验证配置：

```powershell
# 测试MCP服务器
.\scripts\start-mcp-server.ps1 -Test
```

## 📚 参考配置

### 与其他MCP服务器共存

```json
{
  "augment.advanced": {
    "mcpServers": [
      {
        "name": "feedback",
        "command": "F:\\mcp\\Rustmcp\\rust-tauri-mcp-feedback\\src-tauri\\target\\release\\mcp_server.exe",
        "args": [],
        "env": {
          "RUST_LOG": "info",
          "MCP_MODE": "server"
        }
      },
      {
        "name": "mysql",
        "command": "uvx",
        "args": ["--from", "mysql-mcp-server", "mysql_mcp_server"],
        "env": {
          "MYSQL_HOST": "localhost",
          "MYSQL_PORT": "3306",
          "MYSQL_USER": "root",
          "MYSQL_PASSWORD": "password",
          "MYSQL_DATABASE": "mydb"
        }
      },
      {
        "name": "context7",
        "command": "npx",
        "args": ["-y", "@upstash/context7-mcp@latest"]
      }
    ]
  }
}
```

## ✅ 验证安装

配置完成后，您应该能在Augment中看到"feedback"工具可用。可以通过以下方式验证：

1. 在Augment中输入："列出可用的MCP工具"
2. 应该能看到feedback工具在列表中
3. 尝试调用feedback工具测试功能

## 📞 支持

如果遇到问题，请检查：
1. Augment官方文档：https://docs.augmentcode.com/setup-augment/mcp
2. 项目README.md文件
3. 使用测试脚本验证服务器状态
