# MCP Feedback Tool - Rust + Tauri 实现

一个基于Rust和Tauri的MCP (Model Context Protocol) 反馈工具，提供苹果风格的用户界面，支持Markdown渲染和用户反馈收集。

## 🎯 功能特性

- **MCP协议支持**: 完全符合MCP 2024-11-05规范
- **feedback工具**: 显示Markdown内容并收集用户反馈
- **苹果风格UI**: 现代化的用户界面设计
- **SQLite存储**: 持久化反馈数据
- **双模式运行**: 支持独立MCP服务器和GUI应用模式

## 🚀 快速开始

### 构建项目

```bash
# 构建MCP服务器
cd src-tauri
cargo build --release --bin mcp_server

# 构建GUI应用
cargo build --release
```

### 启动MCP服务器

```bash
# 使用启动脚本
.\scripts\start-mcp-server.ps1

# 或直接运行
.\src-tauri\target\release\mcp_server.exe
```

### 测试MCP服务器

```bash
# 运行完整测试
.\scripts\start-mcp-server.ps1 -Test
```

## 📋 Augment配置

### 快速配置
详细配置说明请参考 [AUGMENT_SETUP.md](./AUGMENT_SETUP.md)

**方法1：使用Augment设置面板（推荐）**
1. 打开Augment设置面板
2. 在MCP服务器部分添加：
   - Name: `feedback`
   - Command: `F:\mcp\Rustmcp\rust-tauri-mcp-feedback\src-tauri\target\release\mcp_server.exe`
   - Environment: `RUST_LOG=info`, `MCP_MODE=server`

**方法2：编辑settings.json**
```json
{
  "augment.advanced": {
    "mcpServers": [
      {
        "name": "feedback",
        "command": "F:\\mcp\\Rustmcp\\rust-tauri-mcp-feedback\\src-tauri\\target\\release\\mcp_server.exe",
        "args": [],
        "env": {
          "RUST_LOG": "info",
          "MCP_MODE": "server"
        }
      }
    ]
  }
}
```

### 使用方法

在Augment中，您可以这样使用feedback工具：

```
请使用feedback工具显示以下工作汇报：

## 今日工作完成情况
- ✅ 配置了Rust国内镜像源
- ✅ 修复了编译警告
- ✅ 成功构建了MCP反馈工具
- ✅ 集成了苹果风格UI设计

请收集用户对这些工作的反馈意见。
```

## 🛠️ 工具定义

### feedback工具

**描述**: 显示Markdown内容并收集用户反馈

**参数**:
- `work_summary` (必需): 要显示给用户的Markdown内容
- `title` (可选): 反馈会话的标题，默认为"AI 工作反馈"
- `timeout` (可选): 超时时间（秒），默认为300
- `allow_save` (可选): 是否允许用户保存反馈，默认为true

**示例调用**:
```json
{
  "name": "feedback",
  "arguments": {
    "work_summary": "## 工作汇报\n\n今天完成了以下任务：\n- 任务1\n- 任务2",
    "title": "每日工作反馈",
    "allow_save": true
  }
}
```

## 🏗️ 项目结构

```
rust-tauri-mcp-feedback/
├── src-tauri/                 # Rust后端
│   ├── src/
│   │   ├── bin/
│   │   │   └── mcp_server.rs  # 独立MCP服务器
│   │   ├── mcp/               # MCP协议实现
│   │   ├── storage/           # 数据存储
│   │   ├── ui/                # UI命令处理
│   │   └── lib.rs             # 主库文件
│   └── Cargo.toml
├── src/                       # 前端文件
├── scripts/                   # 脚本文件
│   └── start-mcp-server.ps1   # 启动脚本
├── augment-mcp-config.json    # Augment配置
└── README.md
```

## 🔧 开发说明

### 环境要求
- Rust 1.85.1+
- Node.js 18+
- Windows 10/11

### 镜像源配置
项目已配置国内镜像源以加速构建：
- Cargo: 字节跳动rsproxy.cn
- npm: 淘宝镜像

### 调试模式
```bash
# 启动调试版本
.\scripts\start-mcp-server.ps1 -Debug

# 查看日志
$env:RUST_LOG="debug"
```

## 📝 许可证

MIT License

## 🤝 贡献

欢迎提交Issue和Pull Request！
