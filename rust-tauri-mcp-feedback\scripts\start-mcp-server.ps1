# MCP Feedback Tool 启动脚本
# 用于启动MCP服务器模式

param(
    [switch]$Debug,
    [switch]$Help,
    [switch]$Test
)

if ($Help) {
    Write-Host "MCP Feedback Tool 启动脚本" -ForegroundColor Green
    Write-Host ""
    Write-Host "用法:"
    Write-Host "  .\start-mcp-server.ps1          # 启动Release版本"
    Write-Host "  .\start-mcp-server.ps1 -Debug   # 启动Debug版本"
    Write-Host "  .\start-mcp-server.ps1 -Test    # 测试MCP服务器"
    Write-Host "  .\start-mcp-server.ps1 -Help    # 显示帮助"
    Write-Host ""
    Write-Host "环境变量:"
    Write-Host "  RUST_LOG=info                   # 日志级别"
    Write-Host "  MCP_MODE=server                 # MCP服务器模式"
    exit 0
}

$ErrorActionPreference = "Stop"

# 设置工作目录
$ScriptDir = Split-Path -Parent $MyInvocation.MyCommand.Path
$ProjectRoot = Split-Path -Parent $ScriptDir
Set-Location $ProjectRoot

Write-Host "项目根目录: $ProjectRoot" -ForegroundColor Cyan

# 设置环境变量
$env:RUST_LOG = "info"
$env:MCP_MODE = "server"

# 选择可执行文件
if ($Debug) {
    $ExePath = ".\src-tauri\target\debug\mcp_server.exe"
    Write-Host "启动MCP Feedback Tool服务器 (Debug模式)..." -ForegroundColor Yellow
} else {
    $ExePath = ".\src-tauri\target\release\mcp_server.exe"
    Write-Host "启动MCP Feedback Tool服务器 (Release模式)..." -ForegroundColor Green
}

# 检查文件是否存在
if (-not (Test-Path $ExePath)) {
    Write-Host "错误: 可执行文件不存在: $ExePath" -ForegroundColor Red
    Write-Host "请先运行构建命令:" -ForegroundColor Yellow
    if ($Debug) {
        Write-Host "  cd src-tauri && cargo build --bin mcp_server" -ForegroundColor Cyan
    } else {
        Write-Host "  cd src-tauri && cargo build --release --bin mcp_server" -ForegroundColor Cyan
    }
    exit 1
}

Write-Host "可执行文件: $ExePath" -ForegroundColor Cyan
Write-Host "环境变量: RUST_LOG=$env:RUST_LOG, MCP_MODE=$env:MCP_MODE" -ForegroundColor Cyan
Write-Host ""

if ($Test) {
    Write-Host "测试MCP服务器..." -ForegroundColor Yellow
    
    # 测试初始化
    $initRequest = '{"jsonrpc":"2.0","id":1,"method":"initialize","params":{"protocolVersion":"2024-11-05","capabilities":{},"clientInfo":{"name":"test","version":"1.0.0"}}}'
    Write-Host "发送初始化请求..." -ForegroundColor Cyan
    $initResponse = echo $initRequest | & $ExePath
    Write-Host "初始化响应: $initResponse" -ForegroundColor Green
    
    # 测试工具列表
    $toolsRequest = '{"jsonrpc":"2.0","id":2,"method":"tools/list","params":{}}'
    Write-Host "发送工具列表请求..." -ForegroundColor Cyan
    $toolsResponse = echo $toolsRequest | & $ExePath
    Write-Host "工具列表响应: $toolsResponse" -ForegroundColor Green
    
    # 测试工具调用
    $callRequest = '{"jsonrpc":"2.0","id":3,"method":"tools/call","params":{"name":"feedback","arguments":{"work_summary":"## 测试反馈\n这是一个测试内容","title":"测试会话","allow_save":true}}}'
    Write-Host "发送工具调用请求..." -ForegroundColor Cyan
    $callResponse = echo $callRequest | & $ExePath
    Write-Host "工具调用响应: $callResponse" -ForegroundColor Green
    
    Write-Host "测试完成!" -ForegroundColor Green
} else {
    try {
        # 启动MCP服务器
        Write-Host "MCP服务器正在运行，等待JSON-RPC请求..." -ForegroundColor Green
        Write-Host "按 Ctrl+C 停止服务器" -ForegroundColor Yellow
        & $ExePath
    } catch {
        Write-Host "启动失败: $_" -ForegroundColor Red
        exit 1
    }
}
