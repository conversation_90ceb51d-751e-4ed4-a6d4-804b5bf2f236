// 独立的MCP服务器二进制文件
use std::env;
use tokio::io::{AsyncBufReadExt, AsyncWriteExt, BufReader};
use rust_tauri_mcp_feedback_lib::mcp::{McpServer, JsonRpcRequest, start_sse_server};

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    // 初始化日志到stderr
    env_logger::Builder::from_default_env()
        .target(env_logger::Target::Stderr)
        .init();

    let args: Vec<String> = env::args().collect();
    eprintln!("MCP Server starting with args: {:?}", args);

    // 检查传输模式
    let transport_mode = env::var("MCP_TRANSPORT").unwrap_or_else(|_| "sse".to_string()); // 临时改为SSE测试
    let host = env::var("MCP_HOST").unwrap_or_else(|_| "127.0.0.1".to_string());
    let port = env::var("MCP_PORT")
        .unwrap_or_else(|_| "8000".to_string())
        .parse::<u16>()
        .unwrap_or(8000);

    // 创建MCP服务器实例
    let server = McpServer::new();

    match transport_mode.as_str() {
        "sse" => {
            eprintln!("Starting MCP server in SSE mode on {}:{}", host, port);
            start_sse_server(server, &host, port).await?;
        }
        "stdio" | _ => {
            eprintln!("Starting MCP server in STDIO mode");
            eprintln!("Reading from stdin, writing to stdout");
            run_stdio_server(server).await?;
        }
    }

    Ok(())
}

/// 运行STDIO模式的MCP服务器
async fn run_stdio_server(server: McpServer) -> Result<(), Box<dyn std::error::Error>> {

    let stdin = tokio::io::stdin();
    let mut reader = BufReader::new(stdin);
    let mut line = String::new();

    loop {
        line.clear();
        match reader.read_line(&mut line).await {
            Ok(0) => {
                eprintln!("EOF reached, shutting down");
                break;
            }
            Ok(_) => {
                let input = line.trim();
                if input.is_empty() {
                    continue;
                }

                eprintln!("Received input: {}", input);
                eprintln!("Input length: {}, bytes: {:?}", input.len(), input.as_bytes());

                // 尝试清理输入中的非打印字符
                let cleaned_input = input.chars()
                    .filter(|c| c.is_ascii() && (*c == '\n' || *c == '\r' || *c == '\t' || !c.is_control()))
                    .collect::<String>();

                eprintln!("Cleaned input: {}", cleaned_input);

                // 解析JSON-RPC请求并使用真正的MCP服务器处理
                match serde_json::from_str::<JsonRpcRequest>(&cleaned_input) {
                    Ok(request) => {
                        eprintln!("Parsed request: method={}, id={:?}", request.method, request.id);
                        let response = server.handle_request(request).await;

                        let response_str = serde_json::to_string(&response)?;
                        println!("{}", response_str);
                        tokio::io::stdout().flush().await?;
                        eprintln!("Sent response: {}", response_str);
                    }
                    Err(e) => {
                        eprintln!("Failed to parse JSON-RPC request: {}", e);
                        let error_response = serde_json::json!({
                            "jsonrpc": "2.0",
                            "id": null,
                            "error": {
                                "code": -32700,
                                "message": format!("Parse error: {}", e)
                            }
                        });
                        let error_str = serde_json::to_string(&error_response)?;
                        println!("{}", error_str);
                        tokio::io::stdout().flush().await?;
                    }
                }
            }
            Err(e) => {
                eprintln!("Error reading from stdin: {}", e);
                break;
            }
        }
    }

    eprintln!("MCP Server shutting down");
    Ok(())
}
