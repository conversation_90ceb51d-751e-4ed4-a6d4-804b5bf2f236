// MCP Feedback Tool - Apple Style UI
// 基于 Rust + Tauri 的 MCP 反馈工具

pub mod mcp;
pub mod storage;
pub mod ui;

use std::sync::Arc;
use tauri::Manager;
use tokio::sync::Mutex;
use tokio::io::AsyncWriteExt;

use storage::Database;
use ui::AppState;
use mcp::McpServer;

#[cfg_attr(mobile, tauri::mobile_entry_point)]
pub fn run() {
    // 检查命令行参数
    let args: Vec<String> = std::env::args().collect();
    eprintln!("Command line args: {:?}", args);
    if args.len() > 1 && args[1] == "--mcp-server" {
        eprintln!("Starting MCP server mode");
        run_mcp_server();
        return;
    }
    eprintln!("Starting GUI mode");

    // 初始化日志
    env_logger::init();

    tauri::Builder::default()
        .plugin(tauri_plugin_opener::init())
        .setup(|app| {
            // 获取应用数据目录
            let app_data_dir = app.path().app_data_dir().expect("Failed to get app data dir");
            std::fs::create_dir_all(&app_data_dir).expect("Failed to create app data dir");

            // 初始化数据库
            let db_path = app_data_dir.join("feedback.db");
            let database = Database::new(db_path).expect("Failed to initialize database");

            // 初始化MCP服务器
            let mcp_server = McpServer::new();

            // 创建应用状态
            let app_state = AppState {
                database: Arc::new(Mutex::new(database)),
                pending_feedback: Arc::new(Mutex::new(None)),
            };

            // 管理应用状态
            app.manage(app_state);
            app.manage(mcp_server);

            log::info!("MCP Feedback Tool initialized successfully");
            Ok(())
        })
        .invoke_handler(tauri::generate_handler![
            ui::display_feedback_content,
            ui::submit_user_feedback,
            ui::get_saved_feedbacks,
            ui::delete_feedback_record,
            ui::get_user_settings,
            ui::update_user_settings,
            ui::export_feedbacks,
            ui::handle_mcp_request,
            ui::get_mcp_tools,
            ui::is_mcp_server_initialized,
            ui::update_feedback_record,
            ui::get_feedbacks_by_session,
            ui::set_ai_response_for_feedback,
            ui::add_tag_to_feedback,
            ui::update_feedback_content,
            ui::get_pending_feedback,
            ui::submit_pending_feedback,
        ])
        .run(tauri::generate_context!())
        .expect("error while running tauri application");
}

/// 运行MCP服务器模式
fn run_mcp_server() {
    // 初始化日志到stderr，避免干扰stdout的JSON输出
    env_logger::Builder::from_default_env()
        .target(env_logger::Target::Stderr)
        .init();

    eprintln!("Starting MCP Feedback Tool Server...");

    // 创建MCP服务器
    let server = McpServer::new();

    // 创建tokio运行时
    let rt = tokio::runtime::Runtime::new().expect("Failed to create tokio runtime");

    rt.block_on(async {
        eprintln!("MCP Server is ready to accept connections");
        eprintln!("Available tools: {:?}", mcp::get_available_tools().iter().map(|t| &t.name).collect::<Vec<_>>());

        // 使用tokio的异步stdin处理
        use tokio::io::{AsyncBufReadExt, BufReader};

        let stdin = tokio::io::stdin();
        let mut reader = BufReader::new(stdin);
        let mut line = String::new();

        loop {
            line.clear();
            match reader.read_line(&mut line).await {
                Ok(0) => {
                    // EOF reached
                    eprintln!("EOF reached, shutting down");
                    break;
                }
                Ok(_) => {
                    let input = line.trim();
                    if input.is_empty() {
                        continue;
                    }

                    eprintln!("Received input: {}", input);

                    // 解析JSON-RPC请求
                    match serde_json::from_str::<mcp::JsonRpcRequest>(input) {
                        Ok(request) => {
                            eprintln!("Parsed request: method={}, id={:?}", request.method, request.id);
                            let response = server.handle_request(request).await;

                            // 输出响应到stdout，确保以换行符结尾
                            match serde_json::to_string(&response) {
                                Ok(response_json) => {
                                    println!("{}", response_json);
                                    tokio::io::stdout().flush().await.expect("Failed to flush stdout");
                                    eprintln!("Sent response");
                                }
                                Err(e) => {
                                    eprintln!("Failed to serialize response: {}", e);
                                }
                            }
                        }
                        Err(e) => {
                            eprintln!("Failed to parse JSON-RPC request: {} - Input: {}", e, input);
                            // 发送错误响应
                            let error_response = mcp::JsonRpcResponse {
                                jsonrpc: "2.0".to_string(),
                                id: None,
                                result: None,
                                error: Some(mcp::JsonRpcError {
                                    code: -32700,
                                    message: format!("Parse error: {}", e),
                                    data: None,
                                }),
                            };
                            if let Ok(error_json) = serde_json::to_string(&error_response) {
                                println!("{}", error_json);
                                tokio::io::stdout().flush().await.expect("Failed to flush stdout");
                            }
                        }
                    }
                }
                Err(e) => {
                    eprintln!("Error reading from stdin: {}", e);
                    break;
                }
            }
        }

        eprintln!("MCP Server shutting down");
    });
}
