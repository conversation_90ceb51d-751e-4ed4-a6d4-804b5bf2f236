// MCP 协议定义和 JSON-RPC 2.0 实现

use serde::{Deserialize, Serialize};
use std::collections::HashMap;

/// JSON-RPC 2.0 请求结构
#[derive(Debug, Serialize, Deserialize)]
pub struct JsonRpcRequest {
    pub jsonrpc: String,
    pub method: String,
    pub params: Option<serde_json::Value>,
    pub id: Option<serde_json::Value>,
}

/// JSON-RPC 2.0 响应结构
#[derive(Debug, Serialize, Deserialize)]
pub struct JsonRpcResponse {
    pub jsonrpc: String,
    pub result: Option<serde_json::Value>,
    pub error: Option<JsonRpcError>,
    pub id: Option<serde_json::Value>,
}

impl Default for JsonRpcResponse {
    fn default() -> Self {
        Self {
            jsonrpc: "2.0".to_string(),
            result: None,
            error: None,
            id: None,
        }
    }
}

/// JSON-RPC 2.0 错误结构
#[derive(Debug, Serialize, Deserialize)]
pub struct JsonRpcError {
    pub code: i32,
    pub message: String,
    pub data: Option<serde_json::Value>,
}

/// MCP 工具定义
#[derive(Debug, Serialize, Deserialize)]
pub struct McpTool {
    pub name: String,
    pub description: String,
    #[serde(rename = "inputSchema")]
    pub input_schema: serde_json::Value,
}

/// MCP 工具调用请求
#[derive(Debug, Serialize, Deserialize)]
pub struct ToolCallRequest {
    pub name: String,
    pub arguments: HashMap<String, serde_json::Value>,
}

/// MCP 工具调用响应
#[derive(Debug, Serialize, Deserialize)]
pub struct ToolCallResponse {
    pub content: Vec<ToolResponseContent>,
    #[serde(rename = "isError", skip_serializing_if = "Option::is_none")]
    pub is_error: Option<bool>,
}

/// 工具响应内容
#[derive(Debug, Serialize, Deserialize)]
pub struct ToolResponseContent {
    #[serde(rename = "type")]
    pub content_type: String,
    pub text: String,
}

impl ToolResponseContent {
    pub fn text(text: String) -> Self {
        Self {
            content_type: "text".to_string(),
            text,
        }
    }
}

impl ToolCallResponse {
    pub fn success(content: Vec<ToolResponseContent>) -> Self {
        Self {
            content,
            is_error: None,
        }
    }

    pub fn error(message: String) -> Self {
        Self {
            content: vec![ToolResponseContent::text(message)],
            is_error: Some(true),
        }
    }
}

/// MCP 服务器信息
#[derive(Debug, Serialize, Deserialize)]
pub struct ServerInfo {
    pub name: String,
    pub version: String,
    pub protocol_version: String,
}

/// MCP 客户端信息
#[derive(Debug, Serialize, Deserialize)]
pub struct ClientInfo {
    pub name: String,
    pub version: String,
}

/// 初始化请求
#[derive(Debug, Serialize, Deserialize)]
pub struct InitializeRequest {
    #[serde(rename = "protocolVersion")]
    pub protocol_version: String,
    pub capabilities: ClientCapabilities,
    #[serde(rename = "clientInfo")]
    pub client_info: ClientInfo,
}

/// 客户端能力
#[derive(Debug, Serialize, Deserialize)]
pub struct ClientCapabilities {
    pub tools: Option<ToolsCapability>,
}

/// 工具能力
#[derive(Debug, Serialize, Deserialize)]
pub struct ToolsCapability {
    #[serde(rename = "listChanged")]
    pub list_changed: Option<bool>,
}

/// 初始化响应
#[derive(Debug, Serialize, Deserialize)]
pub struct InitializeResponse {
    #[serde(rename = "protocolVersion")]
    pub protocol_version: String,
    pub capabilities: ServerCapabilities,
    #[serde(rename = "serverInfo")]
    pub server_info: ServerInfo,
}

/// 服务器能力
#[derive(Debug, Serialize, Deserialize)]
pub struct ServerCapabilities {
    pub tools: Option<ToolsCapability>,
}

impl Default for JsonRpcRequest {
    fn default() -> Self {
        Self {
            jsonrpc: "2.0".to_string(),
            method: String::new(),
            params: None,
            id: None,
        }
    }
}


