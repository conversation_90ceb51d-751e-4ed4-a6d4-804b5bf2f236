// MCP 服务器实现

use super::protocol::*;
use super::tools;
use anyhow::Result;
use serde_json::json;
use std::sync::Arc;
use tokio::sync::Mutex;

/// MCP 服务器状态
#[derive(Debug)]
pub struct McpServerState {
    pub initialized: bool,
    pub client_info: Option<ClientInfo>,
}

/// MCP 服务器
pub struct McpServer {
    state: Arc<Mutex<McpServerState>>,
}

impl McpServer {
    /// 创建新的 MCP 服务器
    pub fn new() -> Self {
        Self {
            state: Arc::new(Mutex::new(McpServerState {
                initialized: false,
                client_info: None,
            })),
        }
    }

    /// 处理 JSON-RPC 请求
    pub async fn handle_request(&self, request: JsonRpcRequest) -> JsonRpcResponse {
        let mut response = JsonRpcResponse::default();
        response.id = request.id.clone();

        match request.method.as_str() {
            "initialize" => {
                match self.handle_initialize(request.params).await {
                    Ok(result) => response.result = Some(result),
                    Err(e) => response.error = Some(JsonRpcError {
                        code: -32603,
                        message: e.to_string(),
                        data: None,
                    }),
                }
            }
            "tools/list" => {
                match self.handle_tools_list().await {
                    Ok(result) => response.result = Some(result),
                    Err(e) => response.error = Some(JsonRpcError {
                        code: -32603,
                        message: e.to_string(),
                        data: None,
                    }),
                }
            }
            "tools/call" => {
                match self.handle_tool_call(request.params).await {
                    Ok(result) => response.result = Some(result),
                    Err(e) => response.error = Some(JsonRpcError {
                        code: -32603,
                        message: e.to_string(),
                        data: None,
                    }),
                }
            }
            _ => {
                response.error = Some(JsonRpcError {
                    code: -32601,
                    message: format!("Method not found: {}", request.method),
                    data: None,
                });
            }
        }

        response
    }

    /// 处理初始化请求
    async fn handle_initialize(&self, params: Option<serde_json::Value>) -> Result<serde_json::Value> {
        let init_request: InitializeRequest = match params {
            Some(p) => serde_json::from_value(p)?,
            None => return Err(anyhow::anyhow!("Missing initialization parameters")),
        };

        let mut state = self.state.lock().await;
        state.initialized = true;
        state.client_info = Some(init_request.client_info);

        let response = InitializeResponse {
            protocol_version: "2024-11-05".to_string(),
            capabilities: ServerCapabilities {
                tools: Some(ToolsCapability {
                    list_changed: Some(false),
                }),
            },
            server_info: ServerInfo {
                name: "MCP Feedback Tool".to_string(),
                version: "0.1.0".to_string(),
                protocol_version: "2024-11-05".to_string(),
            },
        };

        Ok(serde_json::to_value(response)?)
    }

    /// 处理工具列表请求
    async fn handle_tools_list(&self) -> Result<serde_json::Value> {
        let tools = tools::get_available_tools();
        Ok(json!({
            "tools": tools
        }))
    }

    /// 处理工具调用请求
    async fn handle_tool_call(&self, params: Option<serde_json::Value>) -> Result<serde_json::Value> {
        let tool_request: ToolCallRequest = match params {
            Some(p) => serde_json::from_value(p)?,
            None => return Err(anyhow::anyhow!("Missing tool call parameters")),
        };

        log::info!("Executing tool: {}", tool_request.name);

        match tools::execute_tool(tool_request).await {
            Ok(response) => {
                log::info!("Tool execution successful");
                Ok(serde_json::to_value(response)?)
            }
            Err(e) => {
                log::error!("Tool execution failed: {}", e);
                // 返回错误响应而不是传播错误
                let error_response = ToolCallResponse::error(format!("工具执行失败: {}", e));
                Ok(serde_json::to_value(error_response)?)
            }
        }
    }

    /// 检查服务器是否已初始化
    pub async fn is_initialized(&self) -> bool {
        let state = self.state.lock().await;
        state.initialized
    }
}

impl Default for McpServer {
    fn default() -> Self {
        Self::new()
    }
}
