// SSE (Server-Sent Events) 传输层实现
// 用于MCP服务器的HTTP SSE通信

use axum::{
    extract::State,
    http::{header, StatusCode},
    response::{IntoResponse, Response, Sse},
    routing::{get, post},
    Json, Router,
};
use futures::{stream, Stream};
use serde_json::Value;
use std::{
    convert::Infallible,
    sync::Arc,
    time::Duration,
};
use tokio::sync::{mpsc, Mutex};
use tower_http::cors::{Any, CorsLayer};

use super::{McpServer, JsonRpcRequest, JsonRpcResponse};

/// SSE服务器状态
#[derive(Clone)]
pub struct SseServerState {
    pub mcp_server: Arc<McpServer>,
    pub event_sender: Arc<Mutex<Option<mpsc::UnboundedSender<String>>>>,
}

impl SseServerState {
    pub fn new(mcp_server: McpServer) -> Self {
        Self {
            mcp_server: Arc::new(mcp_server),
            event_sender: Arc::new(Mutex::new(None)),
        }
    }
}

/// 创建SSE服务器路由
pub fn create_sse_router(state: SseServerState) -> Router {
    Router::new()
        .route("/", get(health_check))
        .route("/health", get(health_check))
        .route("/sse", get(sse_handler))
        .route("/mcp", post(mcp_handler))
        .layer(
            CorsLayer::new()
                .allow_origin(Any)
                .allow_methods(Any)
                .allow_headers(Any),
        )
        .with_state(state)
}

/// 健康检查端点
async fn health_check() -> impl IntoResponse {
    Json(serde_json::json!({
        "status": "ok",
        "service": "MCP Feedback Tool SSE Server",
        "version": "0.1.0"
    }))
}

/// SSE事件流处理器
async fn sse_handler(
    State(state): State<SseServerState>,
) -> Result<Sse<impl Stream<Item = Result<axum::response::sse::Event, Infallible>>>, StatusCode> {
    let (tx, mut rx) = mpsc::unbounded_channel();
    
    // 存储发送器以便后续使用
    {
        let mut sender = state.event_sender.lock().await;
        *sender = Some(tx.clone());
    }

    // 发送初始连接事件
    let _ = tx.send("data: {\"type\":\"connection\",\"status\":\"connected\"}\n\n".to_string());

    // 创建事件流
    let stream = stream::unfold(rx, |mut rx| async move {
        match rx.recv().await {
            Some(data) => {
                let event = axum::response::sse::Event::default()
                    .data(data);
                Some((Ok(event), rx))
            }
            None => None,
        }
    });

    Ok(Sse::new(stream).keep_alive(
        axum::response::sse::KeepAlive::new()
            .interval(Duration::from_secs(30))
            .text("keep-alive"),
    ))
}

/// MCP请求处理器
async fn mcp_handler(
    State(state): State<SseServerState>,
    Json(request): Json<JsonRpcRequest>,
) -> Result<Json<JsonRpcResponse>, StatusCode> {
    log::info!("Received MCP request: method={}, id={:?}", request.method, request.id);

    // 处理MCP请求
    let response = state.mcp_server.handle_request(request).await;

    // 如果有SSE连接，发送响应事件
    if let Some(sender) = state.event_sender.lock().await.as_ref() {
        let event_data = serde_json::json!({
            "type": "mcp_response",
            "response": response
        });
        let _ = sender.send(format!("data: {}\n\n", event_data));
    }

    Ok(Json(response))
}

/// 启动SSE服务器
pub async fn start_sse_server(
    mcp_server: McpServer,
    host: &str,
    port: u16,
) -> Result<(), Box<dyn std::error::Error>> {
    let state = SseServerState::new(mcp_server);
    let app = create_sse_router(state);

    let listener = tokio::net::TcpListener::bind(format!("{}:{}", host, port)).await?;
    
    log::info!("MCP SSE Server starting on http://{}:{}", host, port);
    log::info!("SSE endpoint: http://{}:{}/sse", host, port);
    log::info!("MCP endpoint: http://{}:{}/mcp", host, port);
    log::info!("Health check: http://{}:{}/health", host, port);

    axum::serve(listener, app).await?;

    Ok(())
}

/// SSE客户端连接信息
#[derive(Debug, Clone)]
pub struct SseConnection {
    pub id: String,
    pub connected_at: chrono::DateTime<chrono::Utc>,
}

/// 扩展的SSE服务器状态，包含连接管理
#[derive(Clone)]
pub struct ExtendedSseServerState {
    pub mcp_server: Arc<McpServer>,
    pub connections: Arc<Mutex<Vec<SseConnection>>>,
    pub event_senders: Arc<Mutex<Vec<mpsc::UnboundedSender<String>>>>,
}

impl ExtendedSseServerState {
    pub fn new(mcp_server: McpServer) -> Self {
        Self {
            mcp_server: Arc::new(mcp_server),
            connections: Arc::new(Mutex::new(Vec::new())),
            event_senders: Arc::new(Mutex::new(Vec::new())),
        }
    }

    /// 广播事件到所有连接的客户端
    pub async fn broadcast_event(&self, event: &str) {
        let senders = self.event_senders.lock().await;
        for sender in senders.iter() {
            let _ = sender.send(format!("data: {}\n\n", event));
        }
    }
}
