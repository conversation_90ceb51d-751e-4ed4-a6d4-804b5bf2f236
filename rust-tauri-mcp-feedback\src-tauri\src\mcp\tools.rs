// MCP 工具实现

use super::protocol::*;
use serde_json::json;
use std::collections::HashMap;
use uuid::Uuid;

/// 反馈工具实现
pub struct FeedbackTool;

impl FeedbackTool {
    /// 获取工具定义
    pub fn get_tool_definition() -> McpTool {
        McpTool {
            name: "feedback".to_string(),
            description: "Display markdown content to user and collect feedback with Apple-style UI".to_string(),
            input_schema: json!({
                "type": "object",
                "properties": {
                    "work_summary": {
                        "type": "string",
                        "description": "Markdown content to display to user"
                    },
                    "title": {
                        "type": "string",
                        "description": "Optional title for the feedback session"
                    },
                    "timeout": {
                        "type": "number",
                        "description": "Optional timeout in seconds (default: 300)"
                    },
                    "allow_save": {
                        "type": "boolean",
                        "description": "Whether to allow user to save this feedback (default: true)"
                    }
                },
                "required": ["work_summary"]
            }),
        }
    }

    /// 执行工具调用
    pub async fn execute(arguments: HashMap<String, serde_json::Value>) -> anyhow::Result<ToolCallResponse> {
        // 验证必需参数
        let work_summary = arguments
            .get("work_summary")
            .and_then(|v| v.as_str())
            .ok_or_else(|| anyhow::anyhow!("Missing required parameter: work_summary"))?;

        // 验证work_summary不为空
        if work_summary.trim().is_empty() {
            return Ok(ToolCallResponse::error(
                "work_summary参数不能为空".to_string()
            ));
        }

        let title = arguments
            .get("title")
            .and_then(|v| v.as_str())
            .unwrap_or("AI 工作反馈");

        let timeout = arguments
            .get("timeout")
            .and_then(|v| v.as_f64())
            .unwrap_or(300.0) as u64;

        let allow_save = arguments
            .get("allow_save")
            .and_then(|v| v.as_bool())
            .unwrap_or(true);

        // 记录工具调用
        log::info!("Feedback tool called: title='{}', allow_save={}, timeout={}s", title, allow_save, timeout);

        // 在独立MCP服务器模式下，我们模拟反馈收集过程
        // 在实际的GUI应用中，这里会调用Tauri前端
        match std::env::var("MCP_MODE") {
            Ok(mode) if mode == "server" => {
                // 独立服务器模式：模拟反馈收集
                let session_id = Uuid::new_v4().to_string();

                Ok(ToolCallResponse::success(vec![
                    ToolResponseContent::text(format!(
                        "📋 **反馈工具已启动**\n\n**会话ID**: {}\n**标题**: {}\n**内容预览**: {}...\n**允许保存**: {}\n**超时时间**: {}秒\n\n✅ 反馈收集界面已准备就绪，等待用户输入...",
                        session_id,
                        title,
                        work_summary.chars().take(50).collect::<String>(),
                        allow_save,
                        timeout
                    ))
                ]))
            }
            _ => {
                // GUI模式：调用实际的前端界面
                match crate::ui::show_feedback_dialog(
                    work_summary.to_string(),
                    title.to_string(),
                    timeout,
                    allow_save,
                ).await {
                    Ok(feedback_result) => {
                        Ok(ToolCallResponse::success(vec![
                            ToolResponseContent::text(format!(
                                "✅ **反馈收集完成**\n\n**会话标题**: {}\n**用户反馈**: {}\n**允许保存**: {}\n**处理时间**: {}秒",
                                title, feedback_result, allow_save, timeout
                            ))
                        ]))
                    }
                    Err(e) => {
                        log::error!("Failed to show feedback dialog: {}", e);
                        Ok(ToolCallResponse::error(format!(
                            "反馈界面启动失败: {}", e
                        )))
                    }
                }
            }
        }
    }
}

/// 获取所有可用工具
pub fn get_available_tools() -> Vec<McpTool> {
    vec![FeedbackTool::get_tool_definition()]
}

/// 执行工具调用
pub async fn execute_tool(request: ToolCallRequest) -> anyhow::Result<ToolCallResponse> {
    match request.name.as_str() {
        "feedback" => FeedbackTool::execute(request.arguments).await,
        _ => Err(anyhow::anyhow!("Unknown tool: {}", request.name)),
    }
}
