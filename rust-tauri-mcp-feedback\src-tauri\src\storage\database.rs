// SQLite 数据库操作

use super::models::*;
use anyhow::Result;
use rusqlite::{params, Connection, Row};
use std::path::Path;

/// 数据库管理器
pub struct Database {
    conn: Connection,
}

impl Database {
    /// 创建或打开数据库
    pub fn new<P: AsRef<Path>>(db_path: P) -> Result<Self> {
        let conn = Connection::open(db_path)?;
        let db = Self { conn };
        db.create_tables()?;
        Ok(db)
    }

    /// 创建数据表
    fn create_tables(&self) -> Result<()> {
        // 创建反馈记录表
        self.conn.execute(
            "CREATE TABLE IF NOT EXISTS feedback_records (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                session_id TEXT NOT NULL,
                title TEXT NOT NULL,
                content TEXT NOT NULL,
                user_feedback TEXT NOT NULL,
                ai_response TEXT,
                created_at TEXT NOT NULL,
                updated_at TEXT NOT NULL,
                is_saved BOOLEAN NOT NULL DEFAULT 0,
                tags TEXT NOT NULL DEFAULT '[]',
                rating INTEGER
            )",
            [],
        )?;

        // 创建会话表
        self.conn.execute(
            "CREATE TABLE IF NOT EXISTS feedback_sessions (
                session_id TEXT PRIMARY KEY,
                session_type TEXT NOT NULL,
                created_at TEXT NOT NULL,
                records_count INTEGER NOT NULL DEFAULT 0
            )",
            [],
        )?;

        // 创建用户设置表
        self.conn.execute(
            "CREATE TABLE IF NOT EXISTS user_settings (
                id INTEGER PRIMARY KEY,
                auto_save BOOLEAN NOT NULL DEFAULT 0,
                theme TEXT NOT NULL DEFAULT 'apple',
                font_size INTEGER NOT NULL DEFAULT 14,
                show_line_numbers BOOLEAN NOT NULL DEFAULT 1,
                enable_notifications BOOLEAN NOT NULL DEFAULT 1,
                default_timeout INTEGER NOT NULL DEFAULT 300
            )",
            [],
        )?;

        // 插入默认设置
        self.conn.execute(
            "INSERT OR IGNORE INTO user_settings (id) VALUES (1)",
            [],
        )?;

        Ok(())
    }

    /// 保存反馈记录
    pub fn save_feedback_record(&self, record: &mut FeedbackRecord) -> Result<i64> {
        let tags_json = serde_json::to_string(&record.tags)?;

        let _rows_affected = self.conn.execute(
            "INSERT INTO feedback_records
             (session_id, title, content, user_feedback, ai_response, created_at, updated_at, is_saved, tags, rating)
             VALUES (?1, ?2, ?3, ?4, ?5, ?6, ?7, ?8, ?9, ?10)",
            params![
                record.session_id,
                record.title,
                record.content,
                record.user_feedback,
                record.ai_response,
                record.created_at.to_rfc3339(),
                record.updated_at.to_rfc3339(),
                record.is_saved,
                tags_json,
                record.rating,
            ],
        )?;

        let row_id = self.conn.last_insert_rowid();
        record.id = Some(row_id);
        Ok(row_id)
    }

    /// 更新反馈记录
    pub fn update_feedback_record(&self, record: &FeedbackRecord) -> Result<()> {
        let tags_json = serde_json::to_string(&record.tags)?;

        self.conn.execute(
            "UPDATE feedback_records SET 
             title = ?1, content = ?2, user_feedback = ?3, ai_response = ?4,
             updated_at = ?5, is_saved = ?6, tags = ?7, rating = ?8
             WHERE id = ?9",
            params![
                record.title,
                record.content,
                record.user_feedback,
                record.ai_response,
                record.updated_at.to_rfc3339(),
                record.is_saved,
                tags_json,
                record.rating,
                record.id,
            ],
        )?;

        Ok(())
    }

    /// 获取已保存的反馈记录
    pub fn get_saved_feedbacks(&self) -> Result<Vec<FeedbackRecord>> {
        let mut stmt = self.conn.prepare(
            "SELECT * FROM feedback_records WHERE is_saved = 1 ORDER BY created_at DESC"
        )?;

        let feedback_iter = stmt.query_map([], |row| {
            self.row_to_feedback_record(row)
        })?;

        let mut feedbacks = Vec::new();
        for feedback in feedback_iter {
            feedbacks.push(feedback?);
        }

        Ok(feedbacks)
    }

    /// 根据会话ID获取反馈记录
    pub fn get_feedbacks_by_session(&self, session_id: &str) -> Result<Vec<FeedbackRecord>> {
        let mut stmt = self.conn.prepare(
            "SELECT * FROM feedback_records WHERE session_id = ?1 ORDER BY created_at ASC"
        )?;

        let feedback_iter = stmt.query_map([session_id], |row| {
            self.row_to_feedback_record(row)
        })?;

        let mut feedbacks = Vec::new();
        for feedback in feedback_iter {
            feedbacks.push(feedback?);
        }

        Ok(feedbacks)
    }

    /// 删除反馈记录
    pub fn delete_feedback_record(&self, id: i64) -> Result<()> {
        self.conn.execute(
            "DELETE FROM feedback_records WHERE id = ?1",
            [id],
        )?;
        Ok(())
    }

    /// 获取用户设置
    pub fn get_user_settings(&self) -> Result<UserSettings> {
        let mut stmt = self.conn.prepare(
            "SELECT * FROM user_settings WHERE id = 1"
        )?;

        let settings = stmt.query_row([], |row| {
            Ok(UserSettings {
                id: row.get(0)?,
                auto_save: row.get(1)?,
                theme: row.get(2)?,
                font_size: row.get(3)?,
                show_line_numbers: row.get(4)?,
                enable_notifications: row.get(5)?,
                default_timeout: row.get(6)?,
            })
        })?;

        Ok(settings)
    }

    /// 更新用户设置
    pub fn update_user_settings(&self, settings: &UserSettings) -> Result<()> {
        self.conn.execute(
            "UPDATE user_settings SET 
             auto_save = ?1, theme = ?2, font_size = ?3, 
             show_line_numbers = ?4, enable_notifications = ?5, default_timeout = ?6
             WHERE id = 1",
            params![
                settings.auto_save,
                settings.theme,
                settings.font_size,
                settings.show_line_numbers,
                settings.enable_notifications,
                settings.default_timeout,
            ],
        )?;

        Ok(())
    }

    /// 将数据库行转换为反馈记录
    fn row_to_feedback_record(&self, row: &Row) -> rusqlite::Result<FeedbackRecord> {
        let tags_json: String = row.get(9)?;
        let tags: Vec<String> = serde_json::from_str(&tags_json).unwrap_or_default();

        let created_at_str: String = row.get(6)?;
        let updated_at_str: String = row.get(7)?;

        Ok(FeedbackRecord {
            id: Some(row.get(0)?),
            session_id: row.get(1)?,
            title: row.get(2)?,
            content: row.get(3)?,
            user_feedback: row.get(4)?,
            ai_response: row.get(5)?,
            created_at: chrono::DateTime::parse_from_rfc3339(&created_at_str)
                .unwrap().with_timezone(&chrono::Utc),
            updated_at: chrono::DateTime::parse_from_rfc3339(&updated_at_str)
                .unwrap().with_timezone(&chrono::Utc),
            is_saved: row.get(8)?,
            tags,
            rating: row.get(10)?,
        })
    }
}
