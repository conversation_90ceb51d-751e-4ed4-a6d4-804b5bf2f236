// 数据模型定义

use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};

/// 反馈记录
#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct FeedbackRecord {
    pub id: Option<i64>,
    pub session_id: String,
    pub title: String,
    pub content: String,           // 原始 Markdown 内容
    pub user_feedback: String,     // 用户反馈
    pub ai_response: Option<String>, // AI 后续回应
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
    pub is_saved: bool,           // 用户是否选择保存
    pub tags: Vec<String>,        // 标签分类
    pub rating: Option<i32>,      // 用户评分 (1-5)
}

/// 反馈会话
#[derive(Debug, Serialize, Deserialize)]
pub struct FeedbackSession {
    pub session_id: String,
    pub session_type: String,     // 如："代码审查"、"需求分析"等
    pub created_at: DateTime<Utc>,
    pub records_count: i32,
}

/// 用户设置
#[derive(Debug, Serialize, Deserialize)]
pub struct UserSettings {
    pub id: i64,
    pub auto_save: bool,          // 自动保存反馈
    pub theme: String,            // 主题设置
    pub font_size: i32,           // 字体大小
    pub show_line_numbers: bool,  // 显示行号
    pub enable_notifications: bool, // 启用通知
    pub default_timeout: i32,     // 默认超时时间
}

impl FeedbackRecord {
    /// 创建新的反馈记录
    pub fn new(
        session_id: String,
        title: String,
        content: String,
        user_feedback: String,
    ) -> Self {
        let now = Utc::now();
        Self {
            id: None,
            session_id,
            title,
            content,
            user_feedback,
            ai_response: None,
            created_at: now,
            updated_at: now,
            is_saved: false,
            tags: Vec::new(),
            rating: None,
        }
    }

    /// 更新反馈记录
    pub fn update_feedback(&mut self, feedback: String) {
        self.user_feedback = feedback;
        self.updated_at = Utc::now();
    }

    /// 设置 AI 回应
    pub fn set_ai_response(&mut self, response: String) {
        self.ai_response = Some(response);
        self.updated_at = Utc::now();
    }

    /// 添加标签
    pub fn add_tag(&mut self, tag: String) {
        if !self.tags.contains(&tag) {
            self.tags.push(tag);
            self.updated_at = Utc::now();
        }
    }

    /// 设置评分
    pub fn set_rating(&mut self, rating: i32) {
        if (1..=5).contains(&rating) {
            self.rating = Some(rating);
            self.updated_at = Utc::now();
        }
    }

    /// 标记为已保存
    pub fn mark_as_saved(&mut self) {
        self.is_saved = true;
        self.updated_at = Utc::now();
    }
}

impl Default for UserSettings {
    fn default() -> Self {
        Self {
            id: 1,
            auto_save: false,
            theme: "apple".to_string(),
            font_size: 14,
            show_line_numbers: true,
            enable_notifications: true,
            default_timeout: 300,
        }
    }
}
