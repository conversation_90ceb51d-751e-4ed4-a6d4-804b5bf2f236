// Tauri 命令定义

use crate::storage::{Database, FeedbackRecord, UserSettings};
use crate::mcp::{McpServer, JsonRpcRequest, JsonRpcResponse};
use anyhow::Result;
use std::sync::Arc;
use tauri::State;
use tokio::sync::{Mutex, oneshot};
use uuid::Uuid;

/// 应用状态
pub struct AppState {
    pub database: Arc<Mutex<Database>>,
    pub pending_feedback: Arc<Mutex<Option<PendingFeedback>>>,
}

/// 待处理的反馈
#[derive(Debug)]
pub struct PendingFeedback {
    pub session_id: String,
    pub title: String,
    pub content: String,
    pub allow_save: bool,
    pub sender: oneshot::Sender<String>,
}

/// 反馈对话框显示函数
pub async fn show_feedback_dialog(
    content: String,
    title: String,
    timeout: u64,
    allow_save: bool,
) -> Result<String> {
    use std::sync::Arc;
    use tokio::sync::Mutex;
    use tokio::time::{timeout as tokio_timeout, Duration};

    // 创建全局状态来存储待处理的反馈
    static PENDING_FEEDBACK: once_cell::sync::Lazy<Arc<Mutex<Option<PendingFeedback>>>> =
        once_cell::sync::Lazy::new(|| Arc::new(Mutex::new(None)));

    let (sender, receiver) = oneshot::channel();
    let session_id = Uuid::new_v4().to_string();

    let pending = PendingFeedback {
        session_id: session_id.clone(),
        title: title.clone(),
        content: content.clone(),
        allow_save,
        sender,
    };

    // 存储待处理的反馈
    {
        let mut pending_feedback = PENDING_FEEDBACK.lock().await;
        *pending_feedback = Some(pending);
    }

    log::info!("Feedback dialog created: session_id={}, title='{}', timeout={}s, allow_save={}",
               session_id, title, timeout, allow_save);

    // 在实际的GUI应用中，这里会通过Tauri事件系统通知前端
    // 但在MCP服务器模式下，我们返回一个指示用户需要在GUI中查看的消息
    match std::env::var("MCP_MODE") {
        Ok(mode) if mode == "server" => {
            // MCP服务器模式：返回指导信息
            Ok(format!(
                "📋 **反馈收集已启动**\n\n**会话ID**: {}\n**标题**: {}\n**允许保存**: {}\n**超时**: {}秒\n\n🖥️ **请在GUI应用中查看和提供反馈**\n\n内容预览:\n{}\n\n✅ 反馈会话已准备就绪，请切换到GUI应用程序完成反馈。",
                session_id,
                title,
                allow_save,
                timeout,
                content.chars().take(200).collect::<String>() + if content.len() > 200 { "..." } else { "" }
            ))
        }
        _ => {
            // GUI模式：等待用户反馈
            match tokio_timeout(Duration::from_secs(timeout), receiver).await {
                Ok(Ok(feedback)) => {
                    log::info!("Received user feedback for session {}", session_id);
                    Ok(feedback)
                }
                Ok(Err(_)) => {
                    log::warn!("Feedback sender was dropped for session {}", session_id);
                    Ok(format!("反馈会话已取消 - 会话ID: {}", session_id))
                }
                Err(_) => {
                    log::warn!("Feedback timeout for session {}", session_id);
                    Ok(format!("反馈超时 - 会话ID: {}", session_id))
                }
            }
        }
    }
}

/// 显示反馈内容
#[tauri::command]
pub async fn display_feedback_content(
    content: String,
    title: String,
    allow_save: bool,
    state: State<'_, AppState>,
) -> Result<String, String> {
    let session_id = Uuid::new_v4().to_string();

    // 创建待处理的反馈并存储到状态中
    let (sender, _receiver) = oneshot::channel();
    let pending = PendingFeedback {
        session_id: session_id.clone(),
        title: title.clone(),
        content: content.clone(),
        allow_save,
        sender,
    };

    // 存储到应用状态中
    let mut pending_feedback = state.pending_feedback.lock().await;
    *pending_feedback = Some(pending);

    log::info!("Displaying feedback content: {} (allow_save: {})", title, allow_save);

    Ok(session_id)
}

/// 提交用户反馈
#[tauri::command]
pub async fn submit_user_feedback(
    session_id: String,
    feedback: String,
    should_save: bool,
    rating: Option<i32>,
    state: State<'_, AppState>,
) -> Result<bool, String> {
    let db = state.database.lock().await;

    // 从pending_feedback中获取原始内容和标题
    let pending_feedback_guard = state.pending_feedback.lock().await;
    let (title, content) = if let Some(ref pending) = *pending_feedback_guard {
        if pending.session_id == session_id {
            (pending.title.clone(), pending.content.clone())
        } else {
            ("Unknown Session".to_string(), "No content available".to_string())
        }
    } else {
        ("Unknown Session".to_string(), "No content available".to_string())
    };
    drop(pending_feedback_guard);

    let mut record = FeedbackRecord::new(
        session_id.clone(),
        title,
        content,
        feedback.clone(),
    );

    if let Some(r) = rating {
        record.set_rating(r);
    }

    if should_save {
        record.mark_as_saved();
        db.save_feedback_record(&mut record)
            .map_err(|e| e.to_string())?;
    }

    // 如果有pending feedback，通过sender发送反馈结果
    let mut pending_feedback_guard = state.pending_feedback.lock().await;
    if let Some(pending) = pending_feedback_guard.take() {
        if pending.session_id == session_id {
            // 检查是否允许保存
            if pending.allow_save && should_save {
                log::info!("Feedback will be saved as requested (allow_save: {})", pending.allow_save);
            }
            // 使用sender发送反馈结果
            let _ = pending.sender.send(feedback);
            log::info!("Feedback sent through channel for session: {}", session_id);
        }
    }

    log::info!("User feedback submitted for session: {}", session_id);
    Ok(true)
}

/// 获取已保存的反馈记录
#[tauri::command]
pub async fn get_saved_feedbacks(
    state: State<'_, AppState>,
) -> Result<Vec<FeedbackRecord>, String> {
    let db = state.database.lock().await;
    db.get_saved_feedbacks().map_err(|e| e.to_string())
}

/// 删除反馈记录
#[tauri::command]
pub async fn delete_feedback_record(
    id: i64,
    state: State<'_, AppState>,
) -> Result<bool, String> {
    let db = state.database.lock().await;
    db.delete_feedback_record(id).map_err(|e| e.to_string())?;
    Ok(true)
}

/// 获取用户设置
#[tauri::command]
pub async fn get_user_settings(
    state: State<'_, AppState>,
) -> Result<UserSettings, String> {
    let db = state.database.lock().await;
    db.get_user_settings().map_err(|e| e.to_string())
}

/// 更新用户设置
#[tauri::command]
pub async fn update_user_settings(
    settings: UserSettings,
    state: State<'_, AppState>,
) -> Result<bool, String> {
    let db = state.database.lock().await;
    db.update_user_settings(&settings).map_err(|e| e.to_string())?;
    Ok(true)
}

/// 导出反馈记录
#[tauri::command]
pub async fn export_feedbacks(
    format: String,
    state: State<'_, AppState>,
) -> Result<String, String> {
    let db = state.database.lock().await;
    let records = db.get_saved_feedbacks().map_err(|e| e.to_string())?;
    
    match format.as_str() {
        "json" => {
            serde_json::to_string_pretty(&records).map_err(|e| e.to_string())
        }
        "markdown" => {
            let mut md = String::new();
            md.push_str("# 反馈记录导出\n\n");
            
            for record in records {
                md.push_str(&format!(
                    "## {}\n\n**创建时间**: {}\n\n### 原始内容\n\n{}\n\n### 用户反馈\n\n{}\n\n",
                    record.title,
                    record.created_at.format("%Y-%m-%d %H:%M:%S"),
                    record.content,
                    record.user_feedback
                ));
                
                if let Some(ai_response) = &record.ai_response {
                    md.push_str(&format!("### AI 回应\n\n{}\n\n", ai_response));
                }
                
                if let Some(rating) = record.rating {
                    md.push_str(&format!("**评分**: {}/5\n\n", rating));
                }
                
                md.push_str("---\n\n");
            }
            
            Ok(md)
        }
        _ => Err("不支持的导出格式".to_string()),
    }
}

/// 处理MCP请求
#[tauri::command]
pub async fn handle_mcp_request(
    request: JsonRpcRequest,
    mcp_server: State<'_, McpServer>,
) -> Result<JsonRpcResponse, String> {
    let response = mcp_server.handle_request(request).await;
    Ok(response)
}

/// 获取MCP工具列表
#[tauri::command]
pub async fn get_mcp_tools() -> Result<Vec<crate::mcp::McpTool>, String> {
    Ok(crate::mcp::get_available_tools())
}

/// 检查MCP服务器是否已初始化
#[tauri::command]
pub async fn is_mcp_server_initialized(
    mcp_server: State<'_, McpServer>,
) -> Result<bool, String> {
    Ok(mcp_server.is_initialized().await)
}

/// 获取当前待处理的反馈会话
#[tauri::command]
pub async fn get_pending_feedback() -> Result<Option<serde_json::Value>, String> {
    use std::sync::Arc;
    use tokio::sync::Mutex;

    static PENDING_FEEDBACK: once_cell::sync::Lazy<Arc<Mutex<Option<PendingFeedback>>>> =
        once_cell::sync::Lazy::new(|| Arc::new(Mutex::new(None)));

    let pending_feedback = PENDING_FEEDBACK.lock().await;
    if let Some(ref feedback) = *pending_feedback {
        Ok(Some(serde_json::json!({
            "session_id": feedback.session_id,
            "title": feedback.title,
            "content": feedback.content,
            "allow_save": feedback.allow_save
        })))
    } else {
        Ok(None)
    }
}

/// 提交用户反馈
#[tauri::command]
pub async fn submit_pending_feedback(
    session_id: String,
    user_feedback: String,
    rating: Option<i32>,
) -> Result<bool, String> {
    use std::sync::Arc;
    use tokio::sync::Mutex;

    static PENDING_FEEDBACK: once_cell::sync::Lazy<Arc<Mutex<Option<PendingFeedback>>>> =
        once_cell::sync::Lazy::new(|| Arc::new(Mutex::new(None)));

    let mut pending_feedback = PENDING_FEEDBACK.lock().await;
    if let Some(feedback) = pending_feedback.take() {
        if feedback.session_id == session_id {
            let response = if let Some(r) = rating {
                format!("用户反馈: {}\n评分: {}/5", user_feedback, r)
            } else {
                user_feedback
            };

            // 发送反馈结果
            if feedback.sender.send(response).is_ok() {
                log::info!("Feedback submitted successfully for session {}", session_id);
                Ok(true)
            } else {
                log::warn!("Failed to send feedback for session {}", session_id);
                Ok(false)
            }
        } else {
            Err("Session ID mismatch".to_string())
        }
    } else {
        Err("No pending feedback found".to_string())
    }
}

/// 更新反馈记录
#[tauri::command]
pub async fn update_feedback_record(
    record: FeedbackRecord,
    state: State<'_, AppState>,
) -> Result<bool, String> {
    let db = state.database.lock().await;
    db.update_feedback_record(&record).map_err(|e| e.to_string())?;
    Ok(true)
}

/// 根据会话ID获取反馈记录
#[tauri::command]
pub async fn get_feedbacks_by_session(
    session_id: String,
    state: State<'_, AppState>,
) -> Result<Vec<FeedbackRecord>, String> {
    let db = state.database.lock().await;
    db.get_feedbacks_by_session(&session_id).map_err(|e| e.to_string())
}

/// 为反馈记录设置AI回应
#[tauri::command]
pub async fn set_ai_response_for_feedback(
    feedback_id: i64,
    ai_response: String,
    state: State<'_, AppState>,
) -> Result<bool, String> {
    let db = state.database.lock().await;
    let mut records = db.get_saved_feedbacks().map_err(|e| e.to_string())?;

    if let Some(record) = records.iter_mut().find(|r| r.id == Some(feedback_id)) {
        record.set_ai_response(ai_response);
        db.update_feedback_record(record).map_err(|e| e.to_string())?;
        Ok(true)
    } else {
        Err("反馈记录未找到".to_string())
    }
}

/// 为反馈记录添加标签
#[tauri::command]
pub async fn add_tag_to_feedback(
    feedback_id: i64,
    tag: String,
    state: State<'_, AppState>,
) -> Result<bool, String> {
    let db = state.database.lock().await;
    let mut records = db.get_saved_feedbacks().map_err(|e| e.to_string())?;

    if let Some(record) = records.iter_mut().find(|r| r.id == Some(feedback_id)) {
        record.add_tag(tag);
        db.update_feedback_record(record).map_err(|e| e.to_string())?;
        Ok(true)
    } else {
        Err("反馈记录未找到".to_string())
    }
}

/// 更新反馈记录的用户反馈内容
#[tauri::command]
pub async fn update_feedback_content(
    feedback_id: i64,
    new_feedback: String,
    state: State<'_, AppState>,
) -> Result<bool, String> {
    let db = state.database.lock().await;
    let mut records = db.get_saved_feedbacks().map_err(|e| e.to_string())?;

    if let Some(record) = records.iter_mut().find(|r| r.id == Some(feedback_id)) {
        record.update_feedback(new_feedback);
        db.update_feedback_record(record).map_err(|e| e.to_string())?;
        Ok(true)
    } else {
        Err("反馈记录未找到".to_string())
    }
}
