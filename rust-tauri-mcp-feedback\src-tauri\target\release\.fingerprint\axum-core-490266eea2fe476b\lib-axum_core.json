{"rustc": 12488743700189009532, "features": "[\"tracing\"]", "declared_features": "[\"__private_docs\", \"tracing\"]", "target": 2565713999752801252, "profile": 2040997289075261528, "path": 15702981732299530514, "deps": [[784494742817713399, "tower_service", false, 4978452602139377394], [1906322745568073236, "pin_project_lite", false, 2108799129751499761], [2517136641825875337, "sync_wrapper", false, 1313096218931359091], [7712452662827335977, "tower_layer", false, 7613622479165598088], [7858942147296547339, "rustversion", false, 8588296050048352181], [8606274917505247608, "tracing", false, 8107527640071743983], [9010263965687315507, "http", false, 16675448063919359058], [10229185211513642314, "mime", false, 6157102138744692745], [10629569228670356391, "futures_util", false, 11890112042745754049], [11946729385090170470, "async_trait", false, 6208465529983582724], [14084095096285906100, "http_body", false, 14689707968319332994], [16066129441945555748, "bytes", false, 3688346285058100032], [16900715236047033623, "http_body_util", false, 13204142110714166248]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\axum-core-490266eea2fe476b\\dep-lib-axum_core", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}