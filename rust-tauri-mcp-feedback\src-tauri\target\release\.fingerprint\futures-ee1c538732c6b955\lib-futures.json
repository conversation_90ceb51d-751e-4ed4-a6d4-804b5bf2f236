{"rustc": 12488743700189009532, "features": "[\"alloc\", \"async-await\", \"default\", \"executor\", \"futures-executor\", \"std\"]", "declared_features": "[\"alloc\", \"async-await\", \"bilock\", \"cfg-target-has-atomic\", \"compat\", \"default\", \"executor\", \"futures-executor\", \"io-compat\", \"std\", \"thread-pool\", \"unstable\", \"write-all-vectored\"]", "target": 7465627196321967167, "profile": 18348216721672176038, "path": 2946988075690570688, "deps": [[5103565458935487, "futures_io", false, 8629669290126150252], [1811549171721445101, "futures_channel", false, 13674853722722305132], [7013762810557009322, "futures_sink", false, 11517637101086495947], [7620660491849607393, "futures_core", false, 4986484052472739205], [10629569228670356391, "futures_util", false, 11890112042745754049], [12779779637805422465, "futures_executor", false, 17051180549505857719], [16240732885093539806, "futures_task", false, 5906998868789446260]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\futures-ee1c538732c6b955\\dep-lib-futures", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}