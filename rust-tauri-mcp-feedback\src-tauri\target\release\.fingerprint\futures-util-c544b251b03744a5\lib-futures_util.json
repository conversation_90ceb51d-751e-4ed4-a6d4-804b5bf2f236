{"rustc": 12488743700189009532, "features": "[\"alloc\", \"async-await\", \"async-await-macro\", \"channel\", \"futures-channel\", \"futures-io\", \"futures-macro\", \"futures-sink\", \"io\", \"memchr\", \"sink\", \"slab\", \"std\"]", "declared_features": "[\"alloc\", \"async-await\", \"async-await-macro\", \"bilock\", \"cfg-target-has-atomic\", \"channel\", \"compat\", \"default\", \"futures-channel\", \"futures-io\", \"futures-macro\", \"futures-sink\", \"futures_01\", \"io\", \"io-compat\", \"memchr\", \"portable-atomic\", \"sink\", \"slab\", \"std\", \"tokio-io\", \"unstable\", \"write-all-vectored\"]", "target": 1788798584831431502, "profile": 18348216721672176038, "path": 5251986255978778699, "deps": [[5103565458935487, "futures_io", false, 8629669290126150252], [1615478164327904835, "pin_utils", false, 17178904257535995118], [1811549171721445101, "futures_channel", false, 13674853722722305132], [1906322745568073236, "pin_project_lite", false, 2108799129751499761], [5451793922601807560, "slab", false, 14237809400023880159], [7013762810557009322, "futures_sink", false, 11517637101086495947], [7620660491849607393, "futures_core", false, 4986484052472739205], [10565019901765856648, "futures_macro", false, 4210158407842881529], [15932120279885307830, "memchr", false, 818564849527057580], [16240732885093539806, "futures_task", false, 5906998868789446260]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\futures-util-c544b251b03744a5\\dep-lib-futures_util", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}