{"rustc": 12488743700189009532, "features": "[\"bundled\", \"modern_sqlite\"]", "declared_features": "[\"array\", \"backup\", \"blob\", \"buildtime_bindgen\", \"bundled\", \"bundled-full\", \"bundled-sqlcipher\", \"bundled-sqlcipher-vendored-openssl\", \"bundled-windows\", \"chrono\", \"collation\", \"column_decltype\", \"csv\", \"csvtab\", \"extra_check\", \"functions\", \"hooks\", \"i128_blob\", \"in_gecko\", \"limits\", \"load_extension\", \"loadable_extension\", \"modern-full\", \"modern_sqlite\", \"release_memory\", \"rusqlite-macros\", \"serde_json\", \"serialize\", \"series\", \"session\", \"sqlcipher\", \"time\", \"trace\", \"unlock_notify\", \"url\", \"uuid\", \"vtab\", \"wasm32-wasi-vfs\", \"window\", \"with-asan\"]", "target": 10662205063260755052, "profile": 2040997289075261528, "path": 15763549552834191563, "deps": [[3056352129074654578, "hashlink", false, 11963936014906336217], [3666196340704888985, "smallvec", false, 11138759602371966766], [5510864063823219921, "fallible_streaming_iterator", false, 12576905080607180391], [7896293946984509699, "bitflags", false, 1683892740741937480], [9986166984836792091, "libsqlite3_sys", false, 463138495794332408], [12860549049674006569, "fallible_iterator", false, 16457618607691904293]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\rusqlite-ed9687ca3f6e8966\\dep-lib-rusqlite", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}