{"rustc": 12488743700189009532, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[9388111363236443182, "build_script_build", false, 7864708056157052287], [12092653563678505622, "build_script_build", false, 4332693400290488890], [16702348383442838006, "build_script_build", false, 15577707450105161439]], "local": [{"RerunIfChanged": {"output": "release\\build\\rust-tauri-mcp-feedback-0dde1f8b9d776667\\output", "paths": ["tauri.conf.json", "capabilities"]}}, {"RerunIfEnvChanged": {"var": "TAURI_CONFIG", "val": null}}, {"RerunIfEnvChanged": {"var": "REMOVE_UNUSED_COMMANDS", "val": null}}], "rustflags": [], "config": 0, "compile_kind": 0}