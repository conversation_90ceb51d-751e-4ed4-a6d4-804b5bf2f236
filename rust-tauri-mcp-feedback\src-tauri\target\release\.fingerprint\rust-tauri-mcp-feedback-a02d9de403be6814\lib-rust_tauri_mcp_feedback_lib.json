{"rustc": 12488743700189009532, "features": "[]", "declared_features": "[]", "target": 4845053483811409159, "profile": 2040997289075261528, "path": 10763286916239946207, "deps": [[2706460456408817945, "futures", false, 4732181344480194484], [3601586811267292532, "tower", false, 5403448384174753510], [3722963349756955755, "once_cell", false, 11444511962312206653], [4891297352905791595, "axum", false, 4319405253774667622], [5986029879202738730, "log", false, 15875835707837187817], [6898646762435821041, "env_logger", false, 14915474814967417831], [8319709847752024821, "uuid", false, 13347088972605288554], [8569119365930580996, "serde_json", false, 13356371167341189625], [9388111363236443182, "build_script_build", false, 2297536754704185142], [9689903380558560274, "serde", false, 5693391224771324009], [9897246384292347999, "chrono", false, 11131262852406150706], [11957360342995674422, "hyper", false, 8165731769644970519], [12092653563678505622, "tauri", false, 8032611958481566306], [12944427623413450645, "tokio", false, 15775386897970727871], [13625485746686963219, "anyhow", false, 13002756112068798892], [14435908599267459652, "tower_http", false, 3067582091266180196], [15299814984394074821, "rusqlite", false, 4537055812140246159], [16702348383442838006, "tauri_plugin_opener", false, 1294800592435157458]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\rust-tauri-mcp-feedback-a02d9de403be6814\\dep-lib-rust_tauri_mcp_feedback_lib", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}