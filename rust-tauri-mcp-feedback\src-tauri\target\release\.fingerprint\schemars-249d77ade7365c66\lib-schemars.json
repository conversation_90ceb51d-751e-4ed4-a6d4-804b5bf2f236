{"rustc": 12488743700189009532, "features": "[\"default\", \"derive\", \"indexmap\", \"preserve_order\", \"schemars_derive\", \"url\", \"uuid1\"]", "declared_features": "[\"arrayvec\", \"arrayvec05\", \"arrayvec07\", \"bigdecimal\", \"bigdecimal03\", \"bigdecimal04\", \"bytes\", \"chrono\", \"default\", \"derive\", \"derive_json_schema\", \"either\", \"enumset\", \"impl_json_schema\", \"indexmap\", \"indexmap1\", \"indexmap2\", \"preserve_order\", \"raw_value\", \"rust_decimal\", \"schemars_derive\", \"semver\", \"smallvec\", \"smol_str\", \"ui_test\", \"url\", \"uuid\", \"uuid08\", \"uuid1\"]", "target": 11155677158530064643, "profile": 1369601567987815722, "path": 11377689158968846504, "deps": [[3150220818285335163, "url", false, 12382839495342562446], [6913375703034175521, "build_script_build", false, 5474129528678954014], [6982418085031928086, "dyn_clone", false, 10702610763147943862], [8319709847752024821, "uuid1", false, 923997023810898535], [8569119365930580996, "serde_json", false, 1519571564240149797], [9689903380558560274, "serde", false, 6165232173082432966], [14923790796823607459, "indexmap", false, 16928323582167497369], [16071897500792579091, "schemars_derive", false, 11982572722539914246]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\schemars-249d77ade7365c66\\dep-lib-schemars", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}