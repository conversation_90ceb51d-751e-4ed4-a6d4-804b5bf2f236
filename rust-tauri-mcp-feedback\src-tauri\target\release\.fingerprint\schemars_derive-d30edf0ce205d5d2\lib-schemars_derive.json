{"rustc": 12488743700189009532, "features": "[]", "declared_features": "[]", "target": 2937790071811063934, "profile": 1369601567987815722, "path": 13671213426306106054, "deps": [[3060637413840920116, "proc_macro2", false, 9568900268983983463], [3972868919765946583, "serde_derive_internals", false, 1980701718645614033], [4974441333307933176, "syn", false, 11175117510242224824], [17990358020177143287, "quote", false, 721981653726355831]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\schemars_derive-d30edf0ce205d5d2\\dep-lib-schemars_derive", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}