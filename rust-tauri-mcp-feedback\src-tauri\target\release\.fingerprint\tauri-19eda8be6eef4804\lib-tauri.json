{"rustc": 12488743700189009532, "features": "[\"common-controls-v6\", \"compression\", \"default\", \"dynamic-acl\", \"tauri-runtime-wry\", \"webkit2gtk\", \"webview2-com\", \"wry\", \"x11\"]", "declared_features": "[\"common-controls-v6\", \"compression\", \"config-json5\", \"config-toml\", \"custom-protocol\", \"data-url\", \"default\", \"devtools\", \"dynamic-acl\", \"http-range\", \"image\", \"image-ico\", \"image-png\", \"isolation\", \"linux-libxdo\", \"macos-private-api\", \"macos-proxy\", \"native-tls\", \"native-tls-vendored\", \"objc-exception\", \"process-relaunch-dangerous-allow-symlink-macos\", \"protocol-asset\", \"rustls-tls\", \"specta\", \"tauri-runtime-wry\", \"test\", \"tracing\", \"tray-icon\", \"unstable\", \"uuid\", \"webkit2gtk\", \"webview-data-url\", \"webview2-com\", \"wry\", \"x11\"]", "target": 12223948975794516716, "profile": 2040997289075261528, "path": 90208067532340823, "deps": [[40386456601120721, "percent_encoding", false, 15814842184890035683], [654232091421095663, "tauri_utils", false, 12236939805833611188], [1200537532907108615, "url<PERSON><PERSON>n", false, 4770935109749653526], [1967864351173319501, "muda", false, 14860207537161927282], [2013030631243296465, "webview2_com", false, 12447549457960316321], [3150220818285335163, "url", false, 14892937485446893443], [3331586631144870129, "getrandom", false, 15478399870379543216], [4143744114649553716, "raw_window_handle", false, 8281611408882769585], [4919829919303820331, "serialize_to_javascript", false, 4119934144078829484], [5986029879202738730, "log", false, 15875835707837187817], [8569119365930580996, "serde_json", false, 13356371167341189625], [9010263965687315507, "http", false, 16675448063919359058], [9689903380558560274, "serde", false, 5693391224771324009], [10229185211513642314, "mime", false, 6157102138744692745], [10806645703491011684, "thiserror", false, 9592802430657383282], [11989259058781683633, "dunce", false, 17878304604022778097], [12092653563678505622, "build_script_build", false, 4332693400290488890], [12304025191202589669, "tauri_runtime_wry", false, 887644572984490212], [12565293087094287914, "window_vibrancy", false, 2027304573646768271], [12943761728066819757, "tauri_runtime", false, 8510631188538663564], [12944427623413450645, "tokio", false, 15775386897970727871], [12986574360607194341, "serde_repr", false, 17065767179968179514], [13077543566650298139, "heck", false, 14936390428305917549], [13405681745520956630, "tauri_macros", false, 7712908471755381468], [13625485746686963219, "anyhow", false, 13002756112068798892], [14585479307175734061, "windows", false, 1568146256389624972], [16928111194414003569, "dirs", false, 13272258335355356919], [17155886227862585100, "glob", false, 7065162424542180573]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\tauri-19eda8be6eef4804\\dep-lib-tauri", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}