{"rustc": 12488743700189009532, "features": "[\"config-json\", \"default\"]", "declared_features": "[\"codegen\", \"config-json\", \"config-json5\", \"config-toml\", \"default\", \"isolation\", \"quote\", \"tauri-codegen\"]", "target": 1006236803848883740, "profile": 1369601567987815722, "path": 5635393548781283232, "deps": [[654232091421095663, "tauri_utils", false, 13488275439618237601], [4824857623768494398, "cargo_toml", false, 17168191091825655515], [4899080583175475170, "semver", false, 14067295968080052068], [6913375703034175521, "schemars", false, 10715760201452446453], [7170110829644101142, "json_patch", false, 8185541631669899415], [8569119365930580996, "serde_json", false, 1519571564240149797], [9689903380558560274, "serde", false, 6165232173082432966], [12714016054753183456, "tauri_winres", false, 976850953686247306], [13077543566650298139, "heck", false, 3541629824047730632], [13625485746686963219, "anyhow", false, 3235678686318241737], [15609422047640926750, "toml", false, 10765038149518627926], [15622660310229662834, "walkdir", false, 1077989555320143873], [16928111194414003569, "dirs", false, 1940166168726838361], [17155886227862585100, "glob", false, 11084447232078700594]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\tauri-build-6a8345d4d5bbeb1c\\dep-lib-tauri_build", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}