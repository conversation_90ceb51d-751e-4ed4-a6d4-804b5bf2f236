{"rustc": 12488743700189009532, "features": "[\"brotli\", \"compression\"]", "declared_features": "[\"brotli\", \"compression\", \"config-json5\", \"config-toml\", \"isolation\"]", "target": 17460618180909919773, "profile": 1369601567987815722, "path": 17181063756798201183, "deps": [[654232091421095663, "tauri_utils", false, 13488275439618237601], [3060637413840920116, "proc_macro2", false, 9568900268983983463], [3150220818285335163, "url", false, 12382839495342562446], [4899080583175475170, "semver", false, 14067295968080052068], [4974441333307933176, "syn", false, 11175117510242224824], [7170110829644101142, "json_patch", false, 8185541631669899415], [7392050791754369441, "ico", false, 12748081178650231585], [8319709847752024821, "uuid", false, 923997023810898535], [8569119365930580996, "serde_json", false, 1519571564240149797], [9556762810601084293, "brotli", false, 6506201775983472332], [9689903380558560274, "serde", false, 6165232173082432966], [9857275760291862238, "sha2", false, 3287149504928540008], [10806645703491011684, "thiserror", false, 8336430801292337228], [12687914511023397207, "png", false, 8268787360894764548], [13077212702700853852, "base64", false, 6127982663996667812], [15622660310229662834, "walkdir", false, 1077989555320143873], [17990358020177143287, "quote", false, 721981653726355831]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\tauri-codegen-0c552aaf571a6783\\dep-lib-tauri_codegen", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}