{"rustc": 12488743700189009532, "features": "[\"compression\"]", "declared_features": "[\"compression\", \"config-json5\", \"config-toml\", \"custom-protocol\", \"isolation\", \"tracing\"]", "target": 4649449654257170297, "profile": 1369601567987815722, "path": 12091596292427574350, "deps": [[654232091421095663, "tauri_utils", false, 13488275439618237601], [2704937418414716471, "tauri_codegen", false, 13430037617566071814], [3060637413840920116, "proc_macro2", false, 9568900268983983463], [4974441333307933176, "syn", false, 11175117510242224824], [13077543566650298139, "heck", false, 3541629824047730632], [17990358020177143287, "quote", false, 721981653726355831]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\tauri-macros-27fd0ab2e646c7f4\\dep-lib-tauri_macros", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}