{"rustc": 12488743700189009532, "features": "[]", "declared_features": "[\"devtools\", \"macos-private-api\"]", "target": 10306386172444932100, "profile": 2040997289075261528, "path": 18086563540249045028, "deps": [[654232091421095663, "tauri_utils", false, 12236939805833611188], [3150220818285335163, "url", false, 14892937485446893443], [4143744114649553716, "raw_window_handle", false, 8281611408882769585], [7606335748176206944, "dpi", false, 1853688498743714285], [8569119365930580996, "serde_json", false, 13356371167341189625], [9010263965687315507, "http", false, 16675448063919359058], [9689903380558560274, "serde", false, 5693391224771324009], [10806645703491011684, "thiserror", false, 9592802430657383282], [12943761728066819757, "build_script_build", false, 13214549807184827183], [14585479307175734061, "windows", false, 1568146256389624972], [16727543399706004146, "cookie", false, 16833009753626454333]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\tauri-runtime-7abbf9974e3e2be1\\dep-lib-tauri_runtime", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}