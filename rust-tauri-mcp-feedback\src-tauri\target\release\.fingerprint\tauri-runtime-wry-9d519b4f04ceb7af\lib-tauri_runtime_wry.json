{"rustc": 12488743700189009532, "features": "[\"common-controls-v6\", \"x11\"]", "declared_features": "[\"common-controls-v6\", \"default\", \"devtools\", \"macos-private-api\", \"macos-proxy\", \"objc-exception\", \"tracing\", \"unstable\", \"x11\"]", "target": 1901661049345253480, "profile": 2040997289075261528, "path": 4497968502772010357, "deps": [[376837177317575824, "softbuffer", false, 110997947331422875], [654232091421095663, "tauri_utils", false, 12236939805833611188], [2013030631243296465, "webview2_com", false, 12447549457960316321], [3150220818285335163, "url", false, 14892937485446893443], [3722963349756955755, "once_cell", false, 11444511962312206653], [4143744114649553716, "raw_window_handle", false, 8281611408882769585], [5986029879202738730, "log", false, 15875835707837187817], [8826339825490770380, "tao", false, 3779284228325682131], [9010263965687315507, "http", false, 16675448063919359058], [9141053277961803901, "wry", false, 2433244580554773086], [12304025191202589669, "build_script_build", false, 2857824316782433448], [12943761728066819757, "tauri_runtime", false, 8510631188538663564], [14585479307175734061, "windows", false, 1568146256389624972]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\tauri-runtime-wry-9d519b4f04ceb7af\\dep-lib-tauri_runtime_wry", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}