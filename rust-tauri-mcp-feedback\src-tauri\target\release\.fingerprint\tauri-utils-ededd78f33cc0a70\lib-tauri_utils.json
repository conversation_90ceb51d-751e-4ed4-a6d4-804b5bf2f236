{"rustc": 12488743700189009532, "features": "[\"brotli\", \"compression\", \"resources\", \"walkdir\"]", "declared_features": "[\"aes-gcm\", \"brotli\", \"build\", \"cargo_metadata\", \"compression\", \"config-json5\", \"config-toml\", \"getrandom\", \"html-manipulation\", \"isolation\", \"json5\", \"proc-macro2\", \"process-relaunch-dangerous-allow-symlink-macos\", \"quote\", \"resources\", \"schema\", \"schemars\", \"serialize-to-javascript\", \"swift-rs\", \"walkdir\"]", "target": 7530130812022395703, "profile": 2040997289075261528, "path": 14714332040258902933, "deps": [[1200537532907108615, "url<PERSON><PERSON>n", false, 4770935109749653526], [3150220818285335163, "url", false, 14892937485446893443], [3191507132440681679, "serde_untagged", false, 182494363798936338], [4071963112282141418, "serde_with", false, 14449463599709058791], [4899080583175475170, "semver", false, 291611053939790398], [5986029879202738730, "log", false, 15875835707837187817], [6606131838865521726, "ctor", false, 938590484692860572], [7170110829644101142, "json_patch", false, 11694998425084533993], [8319709847752024821, "uuid", false, 13347088972605288554], [8569119365930580996, "serde_json", false, 13356371167341189625], [9010263965687315507, "http", false, 16675448063919359058], [9451456094439810778, "regex", false, 18170661294241816450], [9556762810601084293, "brotli", false, 12566007563774077154], [9689903380558560274, "serde", false, 5693391224771324009], [10806645703491011684, "thiserror", false, 9592802430657383282], [11989259058781683633, "dunce", false, 17878304604022778097], [13625485746686963219, "anyhow", false, 13002756112068798892], [15609422047640926750, "toml", false, 5277736552303535523], [15622660310229662834, "walkdir", false, 9214153573591194163], [15932120279885307830, "memchr", false, 818564849527057580], [17146114186171651583, "infer", false, 9042933399676428099], [17155886227862585100, "glob", false, 7065162424542180573], [17186037756130803222, "phf", false, 3873167374299123218]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\tauri-utils-ededd78f33cc0a70\\dep-lib-tauri_utils", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}