{"rustc": 12488743700189009532, "features": "[\"default\", \"log\", \"tracing\"]", "declared_features": "[\"__common\", \"balance\", \"buffer\", \"default\", \"discover\", \"filter\", \"full\", \"futures-core\", \"futures-util\", \"hdrhistogram\", \"hedge\", \"indexmap\", \"limit\", \"load\", \"load-shed\", \"log\", \"make\", \"pin-project\", \"pin-project-lite\", \"rand\", \"ready-cache\", \"reconnect\", \"retry\", \"slab\", \"spawn-ready\", \"steer\", \"timeout\", \"tokio\", \"tokio-stream\", \"tokio-util\", \"tracing\", \"util\"]", "target": 3486700084251681313, "profile": 2040997289075261528, "path": 4835995821221137157, "deps": [[784494742817713399, "tower_service", false, 4978452602139377394], [7712452662827335977, "tower_layer", false, 7613622479165598088], [8606274917505247608, "tracing", false, 8107527640071743983]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\tower-c3a07810bc3b6287\\dep-lib-tower", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}