{"rustc": 12488743700189009532, "features": "[\"cors\", \"default\", \"trace\", \"tracing\"]", "declared_features": "[\"add-extension\", \"async-compression\", \"auth\", \"base64\", \"catch-panic\", \"compression-br\", \"compression-deflate\", \"compression-full\", \"compression-gzip\", \"compression-zstd\", \"cors\", \"decompression-br\", \"decompression-deflate\", \"decompression-full\", \"decompression-gzip\", \"decompression-zstd\", \"default\", \"follow-redirect\", \"fs\", \"full\", \"futures-core\", \"futures-util\", \"httpdate\", \"iri-string\", \"limit\", \"map-request-body\", \"map-response-body\", \"metrics\", \"mime\", \"mime_guess\", \"normalize-path\", \"percent-encoding\", \"propagate-header\", \"redirect\", \"request-id\", \"sensitive-headers\", \"set-header\", \"set-status\", \"timeout\", \"tokio\", \"tokio-util\", \"tower\", \"trace\", \"tracing\", \"util\", \"uuid\", \"validate-request\"]", "target": 14850331575045365232, "profile": 2040997289075261528, "path": 16477821813721994333, "deps": [[784494742817713399, "tower_service", false, 4978452602139377394], [1906322745568073236, "pin_project_lite", false, 2108799129751499761], [7712452662827335977, "tower_layer", false, 7613622479165598088], [7896293946984509699, "bitflags", false, 1683892740741937480], [8606274917505247608, "tracing", false, 8107527640071743983], [9010263965687315507, "http", false, 16675448063919359058], [14084095096285906100, "http_body", false, 14689707968319332994], [16066129441945555748, "bytes", false, 3688346285058100032], [16900715236047033623, "http_body_util", false, 13204142110714166248]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\tower-http-9e70d27ade84dcf9\\dep-lib-tower_http", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}