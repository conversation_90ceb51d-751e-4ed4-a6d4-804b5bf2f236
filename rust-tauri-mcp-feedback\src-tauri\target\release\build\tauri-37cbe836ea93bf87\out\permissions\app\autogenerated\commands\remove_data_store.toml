# Copyright 2019-2024 Tauri Programme within The Commons Conservancy
# SPDX-License-Identifier: Apache-2.0
# SPDX-License-Identifier: MIT
# Automatically generated - DO NOT EDIT!

[[permission]]
identifier = "allow-remove-data-store"
description = "Enables the remove_data_store command without any pre-configured scope."
commands.allow = ["remove_data_store"]

[[permission]]
identifier = "deny-remove-data-store"
description = "Denies the remove_data_store command without any pre-configured scope."
commands.deny = ["remove_data_store"]
