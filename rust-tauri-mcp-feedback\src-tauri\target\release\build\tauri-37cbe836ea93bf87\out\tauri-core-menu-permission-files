["\\\\?\\F:\\mcp\\Rustmcp\\rust-tauri-mcp-feedback\\src-tauri\\target\\release\\build\\tauri-37cbe836ea93bf87\\out\\permissions\\menu\\autogenerated\\commands\\append.toml", "\\\\?\\F:\\mcp\\Rustmcp\\rust-tauri-mcp-feedback\\src-tauri\\target\\release\\build\\tauri-37cbe836ea93bf87\\out\\permissions\\menu\\autogenerated\\commands\\create_default.toml", "\\\\?\\F:\\mcp\\Rustmcp\\rust-tauri-mcp-feedback\\src-tauri\\target\\release\\build\\tauri-37cbe836ea93bf87\\out\\permissions\\menu\\autogenerated\\commands\\get.toml", "\\\\?\\F:\\mcp\\Rustmcp\\rust-tauri-mcp-feedback\\src-tauri\\target\\release\\build\\tauri-37cbe836ea93bf87\\out\\permissions\\menu\\autogenerated\\commands\\insert.toml", "\\\\?\\F:\\mcp\\Rustmcp\\rust-tauri-mcp-feedback\\src-tauri\\target\\release\\build\\tauri-37cbe836ea93bf87\\out\\permissions\\menu\\autogenerated\\commands\\is_checked.toml", "\\\\?\\F:\\mcp\\Rustmcp\\rust-tauri-mcp-feedback\\src-tauri\\target\\release\\build\\tauri-37cbe836ea93bf87\\out\\permissions\\menu\\autogenerated\\commands\\is_enabled.toml", "\\\\?\\F:\\mcp\\Rustmcp\\rust-tauri-mcp-feedback\\src-tauri\\target\\release\\build\\tauri-37cbe836ea93bf87\\out\\permissions\\menu\\autogenerated\\commands\\items.toml", "\\\\?\\F:\\mcp\\Rustmcp\\rust-tauri-mcp-feedback\\src-tauri\\target\\release\\build\\tauri-37cbe836ea93bf87\\out\\permissions\\menu\\autogenerated\\commands\\new.toml", "\\\\?\\F:\\mcp\\Rustmcp\\rust-tauri-mcp-feedback\\src-tauri\\target\\release\\build\\tauri-37cbe836ea93bf87\\out\\permissions\\menu\\autogenerated\\commands\\popup.toml", "\\\\?\\F:\\mcp\\Rustmcp\\rust-tauri-mcp-feedback\\src-tauri\\target\\release\\build\\tauri-37cbe836ea93bf87\\out\\permissions\\menu\\autogenerated\\commands\\prepend.toml", "\\\\?\\F:\\mcp\\Rustmcp\\rust-tauri-mcp-feedback\\src-tauri\\target\\release\\build\\tauri-37cbe836ea93bf87\\out\\permissions\\menu\\autogenerated\\commands\\remove.toml", "\\\\?\\F:\\mcp\\Rustmcp\\rust-tauri-mcp-feedback\\src-tauri\\target\\release\\build\\tauri-37cbe836ea93bf87\\out\\permissions\\menu\\autogenerated\\commands\\remove_at.toml", "\\\\?\\F:\\mcp\\Rustmcp\\rust-tauri-mcp-feedback\\src-tauri\\target\\release\\build\\tauri-37cbe836ea93bf87\\out\\permissions\\menu\\autogenerated\\commands\\set_accelerator.toml", "\\\\?\\F:\\mcp\\Rustmcp\\rust-tauri-mcp-feedback\\src-tauri\\target\\release\\build\\tauri-37cbe836ea93bf87\\out\\permissions\\menu\\autogenerated\\commands\\set_as_app_menu.toml", "\\\\?\\F:\\mcp\\Rustmcp\\rust-tauri-mcp-feedback\\src-tauri\\target\\release\\build\\tauri-37cbe836ea93bf87\\out\\permissions\\menu\\autogenerated\\commands\\set_as_help_menu_for_nsapp.toml", "\\\\?\\F:\\mcp\\Rustmcp\\rust-tauri-mcp-feedback\\src-tauri\\target\\release\\build\\tauri-37cbe836ea93bf87\\out\\permissions\\menu\\autogenerated\\commands\\set_as_window_menu.toml", "\\\\?\\F:\\mcp\\Rustmcp\\rust-tauri-mcp-feedback\\src-tauri\\target\\release\\build\\tauri-37cbe836ea93bf87\\out\\permissions\\menu\\autogenerated\\commands\\set_as_windows_menu_for_nsapp.toml", "\\\\?\\F:\\mcp\\Rustmcp\\rust-tauri-mcp-feedback\\src-tauri\\target\\release\\build\\tauri-37cbe836ea93bf87\\out\\permissions\\menu\\autogenerated\\commands\\set_checked.toml", "\\\\?\\F:\\mcp\\Rustmcp\\rust-tauri-mcp-feedback\\src-tauri\\target\\release\\build\\tauri-37cbe836ea93bf87\\out\\permissions\\menu\\autogenerated\\commands\\set_enabled.toml", "\\\\?\\F:\\mcp\\Rustmcp\\rust-tauri-mcp-feedback\\src-tauri\\target\\release\\build\\tauri-37cbe836ea93bf87\\out\\permissions\\menu\\autogenerated\\commands\\set_icon.toml", "\\\\?\\F:\\mcp\\Rustmcp\\rust-tauri-mcp-feedback\\src-tauri\\target\\release\\build\\tauri-37cbe836ea93bf87\\out\\permissions\\menu\\autogenerated\\commands\\set_text.toml", "\\\\?\\F:\\mcp\\Rustmcp\\rust-tauri-mcp-feedback\\src-tauri\\target\\release\\build\\tauri-37cbe836ea93bf87\\out\\permissions\\menu\\autogenerated\\commands\\text.toml", "\\\\?\\F:\\mcp\\Rustmcp\\rust-tauri-mcp-feedback\\src-tauri\\target\\release\\build\\tauri-37cbe836ea93bf87\\out\\permissions\\menu\\autogenerated\\default.toml"]