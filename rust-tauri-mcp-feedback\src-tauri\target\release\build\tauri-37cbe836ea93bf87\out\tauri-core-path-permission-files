["\\\\?\\F:\\mcp\\Rustmcp\\rust-tauri-mcp-feedback\\src-tauri\\target\\release\\build\\tauri-37cbe836ea93bf87\\out\\permissions\\path\\autogenerated\\commands\\basename.toml", "\\\\?\\F:\\mcp\\Rustmcp\\rust-tauri-mcp-feedback\\src-tauri\\target\\release\\build\\tauri-37cbe836ea93bf87\\out\\permissions\\path\\autogenerated\\commands\\dirname.toml", "\\\\?\\F:\\mcp\\Rustmcp\\rust-tauri-mcp-feedback\\src-tauri\\target\\release\\build\\tauri-37cbe836ea93bf87\\out\\permissions\\path\\autogenerated\\commands\\extname.toml", "\\\\?\\F:\\mcp\\Rustmcp\\rust-tauri-mcp-feedback\\src-tauri\\target\\release\\build\\tauri-37cbe836ea93bf87\\out\\permissions\\path\\autogenerated\\commands\\is_absolute.toml", "\\\\?\\F:\\mcp\\Rustmcp\\rust-tauri-mcp-feedback\\src-tauri\\target\\release\\build\\tauri-37cbe836ea93bf87\\out\\permissions\\path\\autogenerated\\commands\\join.toml", "\\\\?\\F:\\mcp\\Rustmcp\\rust-tauri-mcp-feedback\\src-tauri\\target\\release\\build\\tauri-37cbe836ea93bf87\\out\\permissions\\path\\autogenerated\\commands\\normalize.toml", "\\\\?\\F:\\mcp\\Rustmcp\\rust-tauri-mcp-feedback\\src-tauri\\target\\release\\build\\tauri-37cbe836ea93bf87\\out\\permissions\\path\\autogenerated\\commands\\resolve.toml", "\\\\?\\F:\\mcp\\Rustmcp\\rust-tauri-mcp-feedback\\src-tauri\\target\\release\\build\\tauri-37cbe836ea93bf87\\out\\permissions\\path\\autogenerated\\commands\\resolve_directory.toml", "\\\\?\\F:\\mcp\\Rustmcp\\rust-tauri-mcp-feedback\\src-tauri\\target\\release\\build\\tauri-37cbe836ea93bf87\\out\\permissions\\path\\autogenerated\\default.toml"]