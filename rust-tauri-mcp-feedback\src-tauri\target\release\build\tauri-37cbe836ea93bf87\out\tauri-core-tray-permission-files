["\\\\?\\F:\\mcp\\Rustmcp\\rust-tauri-mcp-feedback\\src-tauri\\target\\release\\build\\tauri-37cbe836ea93bf87\\out\\permissions\\tray\\autogenerated\\commands\\get_by_id.toml", "\\\\?\\F:\\mcp\\Rustmcp\\rust-tauri-mcp-feedback\\src-tauri\\target\\release\\build\\tauri-37cbe836ea93bf87\\out\\permissions\\tray\\autogenerated\\commands\\new.toml", "\\\\?\\F:\\mcp\\Rustmcp\\rust-tauri-mcp-feedback\\src-tauri\\target\\release\\build\\tauri-37cbe836ea93bf87\\out\\permissions\\tray\\autogenerated\\commands\\remove_by_id.toml", "\\\\?\\F:\\mcp\\Rustmcp\\rust-tauri-mcp-feedback\\src-tauri\\target\\release\\build\\tauri-37cbe836ea93bf87\\out\\permissions\\tray\\autogenerated\\commands\\set_icon.toml", "\\\\?\\F:\\mcp\\Rustmcp\\rust-tauri-mcp-feedback\\src-tauri\\target\\release\\build\\tauri-37cbe836ea93bf87\\out\\permissions\\tray\\autogenerated\\commands\\set_icon_as_template.toml", "\\\\?\\F:\\mcp\\Rustmcp\\rust-tauri-mcp-feedback\\src-tauri\\target\\release\\build\\tauri-37cbe836ea93bf87\\out\\permissions\\tray\\autogenerated\\commands\\set_menu.toml", "\\\\?\\F:\\mcp\\Rustmcp\\rust-tauri-mcp-feedback\\src-tauri\\target\\release\\build\\tauri-37cbe836ea93bf87\\out\\permissions\\tray\\autogenerated\\commands\\set_show_menu_on_left_click.toml", "\\\\?\\F:\\mcp\\Rustmcp\\rust-tauri-mcp-feedback\\src-tauri\\target\\release\\build\\tauri-37cbe836ea93bf87\\out\\permissions\\tray\\autogenerated\\commands\\set_temp_dir_path.toml", "\\\\?\\F:\\mcp\\Rustmcp\\rust-tauri-mcp-feedback\\src-tauri\\target\\release\\build\\tauri-37cbe836ea93bf87\\out\\permissions\\tray\\autogenerated\\commands\\set_title.toml", "\\\\?\\F:\\mcp\\Rustmcp\\rust-tauri-mcp-feedback\\src-tauri\\target\\release\\build\\tauri-37cbe836ea93bf87\\out\\permissions\\tray\\autogenerated\\commands\\set_tooltip.toml", "\\\\?\\F:\\mcp\\Rustmcp\\rust-tauri-mcp-feedback\\src-tauri\\target\\release\\build\\tauri-37cbe836ea93bf87\\out\\permissions\\tray\\autogenerated\\commands\\set_visible.toml", "\\\\?\\F:\\mcp\\Rustmcp\\rust-tauri-mcp-feedback\\src-tauri\\target\\release\\build\\tauri-37cbe836ea93bf87\\out\\permissions\\tray\\autogenerated\\default.toml"]