<!doctype html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>MCP Feedback Tool</title>

    <!-- 样式文件 -->
    <link rel="stylesheet" href="styles/main.css" />
    <link rel="stylesheet" href="styles/apple-theme.css" />

    <!-- 外部依赖 -->
    <script src="https://cdn.jsdelivr.net/npm/marked@9.1.6/marked.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/prismjs@1.29.0/components/prism-core.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/prismjs@1.29.0/plugins/autoloader/prism-autoloader.min.js"></script>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/prismjs@1.29.0/themes/prism-tomorrow.min.css" />

    <!-- 应用脚本 -->
    <script type="module" src="scripts/main.js" defer></script>
    <script type="module" src="scripts/markdown-renderer.js" defer></script>
    <script type="module" src="scripts/feedback-handler.js" defer></script>
  </head>

  <body>
    <!-- 主容器 -->
    <div class="app-container">
      <!-- 标题栏 -->
      <header class="title-bar">
        <div class="title-bar-content">
          <div class="window-controls">
            <div class="control-button close"></div>
            <div class="control-button minimize"></div>
            <div class="control-button maximize"></div>
          </div>
          <h1 class="app-title">MCP Feedback Tool</h1>
          <div class="status-indicator">
            <span class="status-dot"></span>
            <span class="status-text">就绪</span>
          </div>
        </div>
      </header>

      <!-- 主内容区 -->
      <main class="main-content">
        <!-- 侧边栏 -->
        <aside class="sidebar">
          <nav class="sidebar-nav">
            <button class="nav-item active" data-view="feedback">
              <span class="nav-icon">💬</span>
              <span class="nav-text">反馈</span>
            </button>
            <button class="nav-item" data-view="history">
              <span class="nav-icon">📚</span>
              <span class="nav-text">历史记录</span>
            </button>
            <button class="nav-item" data-view="settings">
              <span class="nav-icon">⚙️</span>
              <span class="nav-text">设置</span>
            </button>
          </nav>
        </aside>

        <!-- 内容区域 -->
        <section class="content-area">
          <!-- 反馈视图 -->
          <div class="view feedback-view active" id="feedback-view">
            <div class="feedback-container">
              <!-- Markdown 内容显示区 -->
              <div class="markdown-container">
                <div class="markdown-header">
                  <h2 class="content-title" id="content-title">等待内容...</h2>
                  <div class="content-actions">
                    <button class="action-btn" id="copy-all-btn" title="复制全部内容">
                      📋
                    </button>
                    <button class="action-btn" id="refresh-btn" title="刷新内容">
                      🔄
                    </button>
                  </div>
                </div>
                <div class="markdown-content" id="markdown-content">
                  <div class="placeholder">
                    <div class="placeholder-icon">📝</div>
                    <p>等待 AI 助手发送反馈内容...</p>
                  </div>
                </div>
              </div>

              <!-- 用户反馈输入区 -->
              <div class="feedback-input-container">
                <div class="input-header">
                  <h3>您的反馈</h3>
                  <div class="input-actions">
                    <label class="save-toggle">
                      <input type="checkbox" id="save-feedback-checkbox" checked>
                      <span class="toggle-slider"></span>
                      <span class="toggle-text">保存反馈</span>
                    </label>
                  </div>
                </div>

                <div class="input-area">
                  <textarea
                    id="user-feedback-input"
                    placeholder="请输入您的反馈意见..."
                    rows="6">
                  </textarea>

                  <div class="rating-container">
                    <span class="rating-label">评分：</span>
                    <div class="rating-stars" id="rating-stars">
                      <span class="star" data-rating="1">⭐</span>
                      <span class="star" data-rating="2">⭐</span>
                      <span class="star" data-rating="3">⭐</span>
                      <span class="star" data-rating="4">⭐</span>
                      <span class="star" data-rating="5">⭐</span>
                    </div>
                  </div>
                </div>

                <div class="action-buttons">
                  <button class="btn btn-secondary" id="clear-btn">
                    清空
                  </button>
                  <button class="btn btn-primary" id="submit-btn">
                    提交反馈
                  </button>
                </div>
              </div>
            </div>
          </div>

          <!-- 历史记录视图 -->
          <div class="view history-view" id="history-view">
            <div class="history-container">
              <div class="history-header">
                <h2>历史记录</h2>
                <div class="history-actions">
                  <button class="btn btn-secondary" id="export-btn">
                    导出记录
                  </button>
                  <button class="btn btn-secondary" id="clear-history-btn">
                    清空历史
                  </button>
                </div>
              </div>
              <div class="history-list" id="history-list">
                <!-- 历史记录将在这里动态加载 -->
              </div>
            </div>
          </div>

          <!-- 设置视图 -->
          <div class="view settings-view" id="settings-view">
            <div class="settings-container">
              <h2>设置</h2>
              <div class="settings-form" id="settings-form">
                <!-- 设置选项将在这里动态加载 -->
              </div>
            </div>
          </div>
        </section>
      </main>

      <!-- 状态栏 -->
      <footer class="status-bar">
        <div class="status-left">
          <span class="connection-status">MCP 服务器: 已连接</span>
        </div>
        <div class="status-right">
          <span class="feedback-count">反馈记录: 0</span>
        </div>
      </footer>
    </div>

    <!-- 模态框 -->
    <div class="modal-overlay" id="modal-overlay">
      <div class="modal">
        <div class="modal-header">
          <h3 class="modal-title" id="modal-title">标题</h3>
          <button class="modal-close" id="modal-close">×</button>
        </div>
        <div class="modal-body" id="modal-body">
          <!-- 模态框内容 -->
        </div>
        <div class="modal-footer">
          <button class="btn btn-secondary" id="modal-cancel">取消</button>
          <button class="btn btn-primary" id="modal-confirm">确认</button>
        </div>
      </div>
    </div>

    <!-- 通知容器 -->
    <div class="notification-container" id="notification-container">
      <!-- 通知将在这里显示 -->
    </div>
  </body>
</html>
