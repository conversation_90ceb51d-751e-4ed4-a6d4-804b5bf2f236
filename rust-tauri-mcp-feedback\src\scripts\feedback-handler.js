// 反馈处理器

export class FeedbackHandler {
    constructor() {
        this.currentSession = null;
        this.autoSaveEnabled = false;
        this.feedbackHistory = [];
    }

    async init() {
        console.log('Feedback handler initialized');
        
        // 加载用户设置
        await this.loadSettings();
        
        // 设置自动保存
        this.setupAutoSave();
        
        // 设置键盘快捷键
        this.setupKeyboardShortcuts();
    }

    async loadSettings() {
        try {
            // 这里可以从后端加载用户设置
            // const settings = await invoke('get_user_settings');
            // this.autoSaveEnabled = settings.auto_save;
        } catch (error) {
            console.error('Error loading settings:', error);
        }
    }

    setupAutoSave() {
        if (!this.autoSaveEnabled) return;

        const feedbackInput = document.getElementById('user-feedback-input');
        if (feedbackInput) {
            let saveTimeout;
            
            feedbackInput.addEventListener('input', () => {
                clearTimeout(saveTimeout);
                saveTimeout = setTimeout(() => {
                    this.saveDraft();
                }, 2000); // 2秒后自动保存草稿
            });
        }
    }

    setupKeyboardShortcuts() {
        document.addEventListener('keydown', (e) => {
            // Ctrl/Cmd + Enter 提交反馈
            if ((e.ctrlKey || e.metaKey) && e.key === 'Enter') {
                e.preventDefault();
                this.submitFeedback();
            }
            
            // Ctrl/Cmd + S 保存草稿
            if ((e.ctrlKey || e.metaKey) && e.key === 's') {
                e.preventDefault();
                this.saveDraft();
            }
            
            // Escape 清空输入
            if (e.key === 'Escape') {
                this.clearInput();
            }
        });
    }

    async submitFeedback() {
        const feedbackInput = document.getElementById('user-feedback-input');
        const saveCheckbox = document.getElementById('save-feedback-checkbox');
        
        if (!feedbackInput) {
            console.error('Feedback input not found');
            return;
        }

        const feedback = feedbackInput.value.trim();
        
        if (!feedback) {
            this.showValidationError('请输入反馈内容');
            return;
        }

        if (feedback.length < 10) {
            this.showValidationError('反馈内容至少需要10个字符');
            return;
        }

        try {
            // 显示提交状态
            this.setSubmitButtonState('submitting');
            
            const shouldSave = saveCheckbox ? saveCheckbox.checked : false;
            const rating = this.getCurrentRating();
            
            // 构建反馈数据
            const feedbackData = {
                content: feedback,
                rating: rating,
                timestamp: new Date().toISOString(),
                shouldSave: shouldSave
            };

            // 提交到后端
            const result = await this.sendFeedbackToBackend(feedbackData);
            
            if (result.success) {
                this.showSuccessMessage('反馈提交成功');
                this.clearInput();
                this.clearDraft();
                
                if (shouldSave) {
                    await this.refreshHistory();
                }
                
                // 触发成功动画
                this.playSuccessAnimation();
            } else {
                throw new Error(result.error || '提交失败');
            }
            
        } catch (error) {
            console.error('Error submitting feedback:', error);
            this.showErrorMessage('提交反馈时出错: ' + error.message);
        } finally {
            this.setSubmitButtonState('normal');
        }
    }

    async sendFeedbackToBackend(feedbackData) {
        // 这里调用 Tauri 后端 API
        try {
            if (typeof invoke !== 'undefined') {
                await invoke('submit_user_feedback', {
                    sessionId: this.currentSession?.id || 'default',
                    feedback: feedbackData.content,
                    shouldSave: feedbackData.shouldSave,
                    rating: feedbackData.rating
                });
                return { success: true };
            } else {
                // 开发模式下的模拟
                console.log('Simulating feedback submission:', feedbackData);
                return { success: true };
            }
        } catch (error) {
            return { success: false, error: error.message };
        }
    }

    getCurrentRating() {
        const activeStars = document.querySelectorAll('.star.active');
        return activeStars.length;
    }

    setRating(rating) {
        const stars = document.querySelectorAll('.star');
        stars.forEach((star, index) => {
            if (index < rating) {
                star.classList.add('active');
            } else {
                star.classList.remove('active');
            }
        });
    }

    clearInput() {
        const feedbackInput = document.getElementById('user-feedback-input');
        if (feedbackInput) {
            feedbackInput.value = '';
        }
        
        this.setRating(0);
        this.clearValidationError();
    }

    async saveDraft() {
        const feedbackInput = document.getElementById('user-feedback-input');
        if (!feedbackInput) return;

        const content = feedbackInput.value.trim();
        if (!content) return;

        try {
            const draft = {
                content: content,
                rating: this.getCurrentRating(),
                timestamp: new Date().toISOString(),
                sessionId: this.currentSession?.id
            };

            // 保存到本地存储
            localStorage.setItem('feedback_draft', JSON.stringify(draft));
            
            // 显示保存指示器
            this.showDraftSavedIndicator();
            
        } catch (error) {
            console.error('Error saving draft:', error);
        }
    }

    loadDraft() {
        try {
            const draftData = localStorage.getItem('feedback_draft');
            if (!draftData) return;

            const draft = JSON.parse(draftData);
            
            // 检查草稿是否过期（24小时）
            const draftAge = Date.now() - new Date(draft.timestamp).getTime();
            if (draftAge > 24 * 60 * 60 * 1000) {
                this.clearDraft();
                return;
            }

            // 恢复草稿内容
            const feedbackInput = document.getElementById('user-feedback-input');
            if (feedbackInput && draft.content) {
                feedbackInput.value = draft.content;
                this.setRating(draft.rating || 0);
                this.showDraftLoadedIndicator();
            }
            
        } catch (error) {
            console.error('Error loading draft:', error);
            this.clearDraft();
        }
    }

    clearDraft() {
        localStorage.removeItem('feedback_draft');
    }

    setSubmitButtonState(state) {
        const submitBtn = document.getElementById('submit-btn');
        if (!submitBtn) return;

        switch (state) {
            case 'submitting':
                submitBtn.disabled = true;
                submitBtn.textContent = '提交中...';
                submitBtn.classList.add('loading');
                break;
            case 'normal':
            default:
                submitBtn.disabled = false;
                submitBtn.textContent = '提交反馈';
                submitBtn.classList.remove('loading');
                break;
        }
    }

    showValidationError(message) {
        this.clearValidationError();
        
        const feedbackInput = document.getElementById('user-feedback-input');
        if (!feedbackInput) return;

        const errorElement = document.createElement('div');
        errorElement.className = 'validation-error';
        errorElement.textContent = message;
        
        feedbackInput.parentNode.insertBefore(errorElement, feedbackInput.nextSibling);
        feedbackInput.classList.add('error');
        
        // 自动清除错误
        setTimeout(() => {
            this.clearValidationError();
        }, 5000);
    }

    clearValidationError() {
        const errorElement = document.querySelector('.validation-error');
        if (errorElement) {
            errorElement.remove();
        }
        
        const feedbackInput = document.getElementById('user-feedback-input');
        if (feedbackInput) {
            feedbackInput.classList.remove('error');
        }
    }

    showSuccessMessage(message) {
        if (window.app) {
            window.app.showNotification(message, 'success');
        }
    }

    showErrorMessage(message) {
        if (window.app) {
            window.app.showNotification(message, 'error');
        }
    }

    showDraftSavedIndicator() {
        // 显示草稿保存指示器
        const indicator = document.createElement('div');
        indicator.className = 'draft-indicator';
        indicator.textContent = '草稿已保存';
        
        document.body.appendChild(indicator);
        
        setTimeout(() => {
            indicator.classList.add('show');
        }, 100);
        
        setTimeout(() => {
            indicator.classList.remove('show');
            setTimeout(() => {
                if (indicator.parentNode) {
                    indicator.parentNode.removeChild(indicator);
                }
            }, 300);
        }, 2000);
    }

    showDraftLoadedIndicator() {
        if (window.app) {
            window.app.showNotification('已恢复草稿内容', 'info');
        }
    }

    playSuccessAnimation() {
        const submitBtn = document.getElementById('submit-btn');
        if (submitBtn) {
            submitBtn.classList.add('success-animation');
            setTimeout(() => {
                submitBtn.classList.remove('success-animation');
            }, 1000);
        }
    }

    async refreshHistory() {
        if (window.app) {
            await window.app.loadHistory();
        }
    }

    // 设置当前会话
    setCurrentSession(session) {
        this.currentSession = session;
    }

    // 获取反馈统计
    getFeedbackStats() {
        return {
            totalFeedbacks: this.feedbackHistory.length,
            averageRating: this.calculateAverageRating(),
            lastFeedbackDate: this.getLastFeedbackDate()
        };
    }

    calculateAverageRating() {
        if (this.feedbackHistory.length === 0) return 0;
        
        const ratingsSum = this.feedbackHistory
            .filter(f => f.rating > 0)
            .reduce((sum, f) => sum + f.rating, 0);
        
        const ratingsCount = this.feedbackHistory.filter(f => f.rating > 0).length;
        
        return ratingsCount > 0 ? (ratingsSum / ratingsCount).toFixed(1) : 0;
    }

    getLastFeedbackDate() {
        if (this.feedbackHistory.length === 0) return null;
        
        const lastFeedback = this.feedbackHistory[this.feedbackHistory.length - 1];
        return new Date(lastFeedback.timestamp);
    }

    // 验证反馈内容
    validateFeedback(content) {
        const errors = [];
        
        if (!content || content.trim().length === 0) {
            errors.push('反馈内容不能为空');
        }
        
        if (content.trim().length < 10) {
            errors.push('反馈内容至少需要10个字符');
        }
        
        if (content.length > 5000) {
            errors.push('反馈内容不能超过5000个字符');
        }
        
        // 检查是否包含敏感词（可选）
        const sensitiveWords = ['spam', 'test123']; // 示例敏感词
        const hasSensitiveWords = sensitiveWords.some(word => 
            content.toLowerCase().includes(word.toLowerCase())
        );
        
        if (hasSensitiveWords) {
            errors.push('反馈内容包含不当词汇');
        }
        
        return {
            isValid: errors.length === 0,
            errors: errors
        };
    }
}
