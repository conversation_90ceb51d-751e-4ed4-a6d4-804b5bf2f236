// 主应用脚本

// 使用全局Tauri API
const { invoke } = window.__TAURI__.core;
import { MarkdownRenderer } from './markdown-renderer.js';
import { FeedbackHandler } from './feedback-handler.js';

class App {
    constructor() {
        this.currentView = 'feedback';
        this.markdownRenderer = new MarkdownRenderer();
        this.feedbackHandler = new FeedbackHandler();
        this.currentSessionId = null;
        
        this.init();
    }

    async init() {
        console.log('Initializing MCP Feedback Tool...');

        try {
            // 初始化事件监听器
            this.initEventListeners();
            console.log('Event listeners initialized');

            // 初始化组件
            await this.markdownRenderer.init();
            console.log('Markdown renderer initialized');

            await this.feedbackHandler.init();
            console.log('Feedback handler initialized');

            // 加载用户设置
            await this.loadUserSettings();
            console.log('User settings loaded');

            // 加载历史记录
            await this.loadHistory();
            console.log('History loaded');

            console.log('App initialized successfully');

            // 测试Tauri连接
            await this.testTauriConnection();

            // 检查是否有待处理的反馈
            await this.checkPendingFeedback();

            // 设置定期检查待处理反馈
            setInterval(() => this.checkPendingFeedback(), 5000);
        } catch (error) {
            console.error('Error during app initialization:', error);
            this.showNotification('应用程序初始化失败: ' + error.message, 'error');
        }
    }

    initEventListeners() {
        // 导航事件
        document.querySelectorAll('.nav-item').forEach(item => {
            item.addEventListener('click', (e) => {
                const view = e.currentTarget.dataset.view;
                this.switchView(view);
            });
        });

        // 反馈提交
        const submitBtn = document.getElementById('submit-btn');
        if (submitBtn) {
            submitBtn.addEventListener('click', () => this.submitFeedback());
        }

        // 清空按钮
        const clearBtn = document.getElementById('clear-btn');
        if (clearBtn) {
            clearBtn.addEventListener('click', () => this.clearFeedback());
        }

        // 复制全部内容
        const copyAllBtn = document.getElementById('copy-all-btn');
        if (copyAllBtn) {
            copyAllBtn.addEventListener('click', () => this.copyAllContent());
        }

        // 刷新内容
        const refreshBtn = document.getElementById('refresh-btn');
        if (refreshBtn) {
            refreshBtn.addEventListener('click', () => this.refreshContent());
        }

        // 评分星星
        document.querySelectorAll('.star').forEach(star => {
            star.addEventListener('click', (e) => {
                const rating = parseInt(e.target.dataset.rating);
                this.setRating(rating);
            });
        });

        // 模态框关闭
        const modalClose = document.getElementById('modal-close');
        const modalOverlay = document.getElementById('modal-overlay');
        if (modalClose) {
            modalClose.addEventListener('click', () => this.closeModal());
        }
        if (modalOverlay) {
            modalOverlay.addEventListener('click', (e) => {
                if (e.target === modalOverlay) {
                    this.closeModal();
                }
            });
        }

        // 历史记录操作
        const exportBtn = document.getElementById('export-btn');
        if (exportBtn) {
            exportBtn.addEventListener('click', () => this.exportHistory());
        }

        // 键盘快捷键
        document.addEventListener('keydown', (e) => {
            if (e.ctrlKey || e.metaKey) {
                switch (e.key) {
                    case 'Enter':
                        e.preventDefault();
                        this.submitFeedback();
                        break;
                    case 's':
                        e.preventDefault();
                        this.toggleSaveOption();
                        break;
                }
            }
        });
    }

    switchView(viewName) {
        // 更新导航状态
        document.querySelectorAll('.nav-item').forEach(item => {
            item.classList.remove('active');
        });
        document.querySelector(`[data-view="${viewName}"]`).classList.add('active');

        // 切换视图
        document.querySelectorAll('.view').forEach(view => {
            view.classList.remove('active');
        });
        document.getElementById(`${viewName}-view`).classList.add('active');

        this.currentView = viewName;

        // 根据视图执行特定操作
        switch (viewName) {
            case 'history':
                this.loadHistory();
                break;
            case 'settings':
                this.loadSettings();
                break;
        }
    }

    async displayContent(content, title = '反馈内容', allowSave = true) {
        try {
            // 更新标题
            document.getElementById('content-title').textContent = title;
            
            // 渲染 Markdown 内容
            await this.markdownRenderer.render(content);
            
            // 设置保存选项
            const saveCheckbox = document.getElementById('save-feedback-checkbox');
            if (saveCheckbox) {
                saveCheckbox.checked = allowSave;
                saveCheckbox.disabled = !allowSave;
            }
            
            // 生成会话ID
            this.currentSessionId = await invoke('display_feedback_content', {
                content,
                title,
                allowSave
            });
            
            // 切换到反馈视图
            this.switchView('feedback');
            
            // 清空之前的输入
            this.clearFeedback();
            
            console.log('Content displayed successfully');
        } catch (error) {
            console.error('Error displaying content:', error);
            this.showNotification('显示内容时出错', 'error');
        }
    }

    async submitFeedback() {
        const feedbackInput = document.getElementById('user-feedback-input');
        const saveCheckbox = document.getElementById('save-feedback-checkbox');
        const feedback = feedbackInput.value.trim();

        if (!feedback) {
            this.showNotification('请输入反馈内容', 'warning');
            return;
        }

        try {
            const rating = this.getCurrentRating();

            if (this.currentSessionId) {
                // 提交MCP反馈会话
                const success = await invoke('submit_pending_feedback', {
                    sessionId: this.currentSessionId,
                    userFeedback: feedback,
                    rating: rating > 0 ? rating : null
                });

                if (success) {
                    this.showNotification('反馈提交成功', 'success');
                    this.currentSessionId = null;
                } else {
                    this.showNotification('反馈提交失败', 'error');
                    return;
                }
            } else {
                // 常规反馈保存
                const shouldSave = saveCheckbox.checked;
                if (shouldSave) {
                    await invoke('submit_user_feedback', {
                        feedback,
                        rating: rating > 0 ? rating : null
                    });
                    this.showNotification('反馈已保存', 'success');
                    await this.loadHistory();
                } else {
                    this.showNotification('反馈已提交（未保存）', 'info');
                }
            }

            // 清空输入
            this.clearFeedback();
            
        } catch (error) {
            console.error('Error submitting feedback:', error);
            this.showNotification('提交反馈时出错', 'error');
        }
    }

    clearFeedback() {
        const feedbackInput = document.getElementById('user-feedback-input');
        if (feedbackInput) {
            feedbackInput.value = '';
        }
        
        // 重置评分
        this.setRating(0);
    }

    async copyAllContent() {
        try {
            const content = document.getElementById('markdown-content').textContent;
            await navigator.clipboard.writeText(content);
            this.showNotification('内容已复制到剪贴板', 'success');
        } catch (error) {
            console.error('Error copying content:', error);
            this.showNotification('复制失败', 'error');
        }
    }

    refreshContent() {
        // 重新渲染当前内容
        this.showNotification('内容已刷新', 'success');
    }

    setRating(rating) {
        document.querySelectorAll('.star').forEach((star, index) => {
            if (index < rating) {
                star.classList.add('active');
            } else {
                star.classList.remove('active');
            }
        });
    }

    getCurrentRating() {
        const activeStars = document.querySelectorAll('.star.active');
        return activeStars.length;
    }

    toggleSaveOption() {
        const saveCheckbox = document.getElementById('save-feedback-checkbox');
        if (saveCheckbox && !saveCheckbox.disabled) {
            saveCheckbox.checked = !saveCheckbox.checked;
        }
    }

    async loadUserSettings() {
        try {
            const settings = await invoke('get_user_settings');
            console.log('User settings loaded:', settings);
            // 应用设置到界面
        } catch (error) {
            console.error('Error loading user settings:', error);
        }
    }

    async loadHistory() {
        try {
            const feedbacks = await invoke('get_saved_feedbacks');
            this.renderHistoryList(feedbacks);
            
            // 更新状态栏
            const statusElement = document.querySelector('.feedback-count');
            if (statusElement) {
                statusElement.textContent = `反馈记录: ${feedbacks.length}`;
            }
        } catch (error) {
            console.error('Error loading history:', error);
        }
    }

    renderHistoryList(feedbacks) {
        const historyList = document.getElementById('history-list');
        if (!historyList) return;
        
        if (feedbacks.length === 0) {
            historyList.innerHTML = `
                <div class="placeholder">
                    <div class="placeholder-icon">📝</div>
                    <p>暂无保存的反馈记录</p>
                </div>
            `;
            return;
        }
        
        historyList.innerHTML = feedbacks.map(feedback => `
            <div class="history-item" data-id="${feedback.id}">
                <div class="history-item-header">
                    <div class="history-item-title">${feedback.title}</div>
                    <div class="history-item-date">${new Date(feedback.created_at).toLocaleDateString()}</div>
                </div>
                <div class="history-item-preview">${feedback.user_feedback}</div>
            </div>
        `).join('');
        
        // 添加点击事件
        historyList.querySelectorAll('.history-item').forEach(item => {
            item.addEventListener('click', () => {
                const id = item.dataset.id;
                this.viewHistoryItem(id);
            });
        });
    }

    async loadSettings() {
        // 加载设置界面
        console.log('Loading settings...');
    }

    async exportHistory() {
        try {
            const format = 'markdown'; // 可以让用户选择格式
            const exportData = await invoke('export_feedbacks', { format });
            
            // 创建下载链接
            const blob = new Blob([exportData], { type: 'text/markdown' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `feedback-export-${new Date().toISOString().split('T')[0]}.md`;
            a.click();
            URL.revokeObjectURL(url);
            
            this.showNotification('导出成功', 'success');
        } catch (error) {
            console.error('Error exporting history:', error);
            this.showNotification('导出失败', 'error');
        }
    }

    viewHistoryItem(id) {
        // 显示历史记录详情
        console.log('Viewing history item:', id);
    }

    showModal(title, content) {
        const modal = document.getElementById('modal-overlay');
        const modalTitle = document.getElementById('modal-title');
        const modalBody = document.getElementById('modal-body');
        
        if (modal && modalTitle && modalBody) {
            modalTitle.textContent = title;
            modalBody.innerHTML = content;
            modal.classList.add('active');
        }
    }

    closeModal() {
        const modal = document.getElementById('modal-overlay');
        if (modal) {
            modal.classList.remove('active');
        }
    }

    showNotification(message, type = 'success') {
        const container = document.getElementById('notification-container');
        if (!container) return;
        
        const notification = document.createElement('div');
        notification.className = `notification ${type}`;
        
        const icons = {
            success: '✅',
            error: '❌',
            warning: '⚠️',
            info: 'ℹ️'
        };
        
        notification.innerHTML = `
            <div class="notification-content">
                <span class="notification-icon">${icons[type] || icons.info}</span>
                <span class="notification-text">${message}</span>
            </div>
        `;
        
        container.appendChild(notification);
        
        // 显示动画
        setTimeout(() => notification.classList.add('show'), 100);
        
        // 自动移除
        setTimeout(() => {
            notification.classList.remove('show');
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 300);
        }, 3000);
    }

    async testTauriConnection() {
        try {
            console.log('Testing Tauri connection...');
            const result = await invoke('is_mcp_server_initialized');
            console.log('Tauri connection test result:', result);
            this.showNotification('应用程序连接正常', 'success');
        } catch (error) {
            console.error('Tauri connection test failed:', error);
            this.showNotification('后端连接失败: ' + error.message, 'error');
        }
    }

    async checkPendingFeedback() {
        try {
            const pendingFeedback = await invoke('get_pending_feedback');
            if (pendingFeedback) {
                console.log('Found pending feedback:', pendingFeedback);
                this.displayPendingFeedback(pendingFeedback);
            }
        } catch (error) {
            console.error('Error checking pending feedback:', error);
        }
    }

    displayPendingFeedback(feedbackData) {
        // 切换到反馈视图
        this.switchView('feedback');

        // 更新标题
        const titleElement = document.getElementById('content-title');
        if (titleElement) {
            titleElement.textContent = feedbackData.title || '反馈请求';
        }

        // 显示内容
        this.markdownRenderer.render(feedbackData.content);

        // 显示反馈输入区域
        const feedbackContainer = document.querySelector('.feedback-input-container');
        if (feedbackContainer) {
            feedbackContainer.style.display = 'block';
        }

        // 存储会话ID
        this.currentSessionId = feedbackData.session_id;

        // 更新保存选项
        const saveCheckbox = document.getElementById('save-feedback-checkbox');
        if (saveCheckbox) {
            saveCheckbox.checked = feedbackData.allow_save;
            saveCheckbox.disabled = !feedbackData.allow_save;
        }

        // 显示通知
        this.showNotification('收到新的反馈请求', 'info');

        // 聚焦到反馈输入框
        const feedbackInput = document.getElementById('user-feedback-input');
        if (feedbackInput) {
            feedbackInput.focus();
        }
    }
}

// 初始化应用
document.addEventListener('DOMContentLoaded', () => {
    window.app = new App();
});

// 导出给全局使用
window.displayFeedbackContent = (content, title, allowSave) => {
    if (window.app) {
        window.app.displayContent(content, title, allowSave);
    }
};
