// Markdown 渲染器

export class MarkdownRenderer {
    constructor() {
        this.container = null;
        this.renderer = null;
    }

    async init() {
        this.container = document.getElementById('markdown-content');
        
        if (!this.container) {
            throw new Error('Markdown container not found');
        }

        // 配置 marked
        if (typeof marked !== 'undefined') {
            this.renderer = new marked.Renderer();
            this.setupRenderer();
            this.configureMarked();
        } else {
            console.warn('Marked library not loaded');
        }

        console.log('Markdown renderer initialized');
    }

    setupRenderer() {
        // 自定义代码块渲染
        this.renderer.code = (code, language) => {
            const validLanguage = language && Prism.languages[language] ? language : 'text';
            const highlightedCode = Prism.highlight(code, Prism.languages[validLanguage] || Prism.languages.text, validLanguage);
            
            return `
                <pre class="language-${validLanguage}">
                    <button class="copy-code-btn" onclick="copyCode(this)">复制</button>
                    <code class="language-${validLanguage}">${highlightedCode}</code>
                </pre>
            `;
        };

        // 自定义链接渲染
        this.renderer.link = (href, title, text) => {
            const titleAttr = title ? ` title="${title}"` : '';
            return `<a href="${href}"${titleAttr} target="_blank" rel="noopener noreferrer">${text}</a>`;
        };

        // 自定义表格渲染
        this.renderer.table = (header, body) => {
            return `
                <div class="table-container">
                    <table class="markdown-table">
                        <thead>${header}</thead>
                        <tbody>${body}</tbody>
                    </table>
                </div>
            `;
        };

        // 自定义列表渲染
        this.renderer.list = (body, ordered, start) => {
            const type = ordered ? 'ol' : 'ul';
            const startAttr = ordered && start !== 1 ? ` start="${start}"` : '';
            return `<${type}${startAttr} class="markdown-list">${body}</${type}>`;
        };

        // 自定义引用渲染
        this.renderer.blockquote = (quote) => {
            return `<blockquote class="markdown-blockquote">${quote}</blockquote>`;
        };
    }

    configureMarked() {
        marked.setOptions({
            renderer: this.renderer,
            highlight: function(code, lang) {
                if (lang && Prism.languages[lang]) {
                    return Prism.highlight(code, Prism.languages[lang], lang);
                }
                return code;
            },
            pedantic: false,
            gfm: true,
            breaks: true,
            sanitize: false,
            smartLists: true,
            smartypants: false,
            xhtml: false
        });
    }

    async render(markdownText) {
        if (!this.container) {
            throw new Error('Markdown container not initialized');
        }

        try {
            // 清空容器
            this.container.innerHTML = '';

            if (!markdownText || markdownText.trim() === '') {
                this.showPlaceholder();
                return;
            }

            // 渲染 Markdown
            const html = marked.parse(markdownText);
            this.container.innerHTML = html;

            // 添加复制按钮到代码块
            this.addCopyButtons();

            // 添加表格样式
            this.styleElements();

            // 处理数学公式（如果需要）
            this.processMath();

            console.log('Markdown rendered successfully');
        } catch (error) {
            console.error('Error rendering markdown:', error);
            this.showError('渲染 Markdown 时出错');
        }
    }

    showPlaceholder() {
        this.container.innerHTML = `
            <div class="placeholder">
                <div class="placeholder-icon">📝</div>
                <p>等待 AI 助手发送反馈内容...</p>
            </div>
        `;
    }

    showError(message) {
        this.container.innerHTML = `
            <div class="placeholder error">
                <div class="placeholder-icon">❌</div>
                <p>${message}</p>
            </div>
        `;
    }

    addCopyButtons() {
        const codeBlocks = this.container.querySelectorAll('pre code');
        
        codeBlocks.forEach((block, index) => {
            const pre = block.parentElement;
            
            // 检查是否已经有复制按钮
            if (pre.querySelector('.copy-code-btn')) {
                return;
            }

            const button = document.createElement('button');
            button.className = 'copy-code-btn';
            button.textContent = '复制';
            button.onclick = () => this.copyCode(button);
            
            pre.style.position = 'relative';
            pre.appendChild(button);
        });
    }

    async copyCode(button) {
        try {
            const pre = button.parentElement;
            const code = pre.querySelector('code');
            const text = code.textContent;
            
            await navigator.clipboard.writeText(text);
            
            // 更新按钮状态
            const originalText = button.textContent;
            button.textContent = '已复制';
            button.style.backgroundColor = '#34C759';
            
            setTimeout(() => {
                button.textContent = originalText;
                button.style.backgroundColor = '';
            }, 2000);
            
            // 显示通知
            if (window.app) {
                window.app.showNotification('代码已复制到剪贴板', 'success');
            }
        } catch (error) {
            console.error('Error copying code:', error);
            if (window.app) {
                window.app.showNotification('复制失败', 'error');
            }
        }
    }

    styleElements() {
        // 为表格添加样式类
        const tables = this.container.querySelectorAll('table');
        tables.forEach(table => {
            if (!table.classList.contains('markdown-table')) {
                table.classList.add('markdown-table');
            }
            
            // 包装表格以支持横向滚动
            if (!table.parentElement.classList.contains('table-container')) {
                const wrapper = document.createElement('div');
                wrapper.className = 'table-container';
                table.parentNode.insertBefore(wrapper, table);
                wrapper.appendChild(table);
            }
        });

        // 为图片添加样式
        const images = this.container.querySelectorAll('img');
        images.forEach(img => {
            img.classList.add('markdown-image');
            img.loading = 'lazy';
        });

        // 为链接添加外部链接图标
        const links = this.container.querySelectorAll('a[href^="http"]');
        links.forEach(link => {
            if (!link.querySelector('.external-link-icon')) {
                const icon = document.createElement('span');
                icon.className = 'external-link-icon';
                icon.innerHTML = ' 🔗';
                link.appendChild(icon);
            }
        });
    }

    processMath() {
        // 如果需要支持数学公式，可以在这里集成 MathJax 或 KaTeX
        // 目前暂时跳过
    }

    // 获取渲染后的纯文本内容
    getTextContent() {
        return this.container ? this.container.textContent : '';
    }

    // 获取渲染后的 HTML 内容
    getHtmlContent() {
        return this.container ? this.container.innerHTML : '';
    }

    // 清空内容
    clear() {
        if (this.container) {
            this.container.innerHTML = '';
        }
    }

    // 搜索内容
    search(query) {
        if (!this.container || !query) {
            return [];
        }

        const text = this.container.textContent.toLowerCase();
        const searchQuery = query.toLowerCase();
        const results = [];
        
        let index = text.indexOf(searchQuery);
        while (index !== -1) {
            results.push({
                index,
                context: text.substring(Math.max(0, index - 50), index + searchQuery.length + 50)
            });
            index = text.indexOf(searchQuery, index + 1);
        }
        
        return results;
    }

    // 高亮搜索结果
    highlightSearch(query) {
        if (!this.container || !query) {
            return;
        }

        // 移除之前的高亮
        this.removeHighlight();

        // 添加新的高亮
        const walker = document.createTreeWalker(
            this.container,
            NodeFilter.SHOW_TEXT,
            null,
            false
        );

        const textNodes = [];
        let node;
        while (node = walker.nextNode()) {
            textNodes.push(node);
        }

        textNodes.forEach(textNode => {
            const text = textNode.textContent;
            const regex = new RegExp(`(${query})`, 'gi');
            
            if (regex.test(text)) {
                const highlightedText = text.replace(regex, '<mark class="search-highlight">$1</mark>');
                const wrapper = document.createElement('span');
                wrapper.innerHTML = highlightedText;
                textNode.parentNode.replaceChild(wrapper, textNode);
            }
        });
    }

    // 移除搜索高亮
    removeHighlight() {
        if (!this.container) return;

        const highlights = this.container.querySelectorAll('.search-highlight');
        highlights.forEach(highlight => {
            const parent = highlight.parentNode;
            parent.replaceChild(document.createTextNode(highlight.textContent), highlight);
            parent.normalize();
        });
    }
}

// 全局复制代码函数
window.copyCode = async function(button) {
    if (window.app && window.app.markdownRenderer) {
        await window.app.markdownRenderer.copyCode(button);
    }
};
