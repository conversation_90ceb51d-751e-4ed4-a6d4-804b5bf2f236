/* 苹果风格主题样式 */

/* 反馈视图样式 */
.feedback-container {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
  height: 100%;
}

/* Markdown 容器 */
.markdown-container {
  flex: 1;
  background-color: var(--bg-tertiary);
  border-radius: var(--radius-large);
  border: 1px solid var(--border-color);
  overflow: hidden;
  box-shadow: var(--shadow-light);
}

.markdown-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-md);
  background-color: var(--bg-secondary);
  border-bottom: 1px solid var(--separator-color);
}

.content-title {
  font-size: var(--font-size-lg);
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
}

.content-actions {
  display: flex;
  gap: var(--spacing-xs);
}

.action-btn {
  width: 32px;
  height: 32px;
  border: none;
  background-color: var(--bg-quaternary);
  border-radius: var(--radius-small);
  cursor: pointer;
  transition: all var(--transition-fast);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--font-size-sm);
}

.action-btn:hover {
  background-color: var(--primary-color);
  color: white;
  transform: scale(1.05);
}

.markdown-content {
  padding: var(--spacing-lg);
  overflow-y: auto;
  max-height: 400px;
  line-height: 1.6;
}

/* Markdown 样式 */
.markdown-content h1,
.markdown-content h2,
.markdown-content h3,
.markdown-content h4,
.markdown-content h5,
.markdown-content h6 {
  margin: var(--spacing-lg) 0 var(--spacing-md) 0;
  font-weight: 600;
  color: var(--text-primary);
}

.markdown-content h1 { font-size: var(--font-size-xxl); }
.markdown-content h2 { font-size: var(--font-size-xl); }
.markdown-content h3 { font-size: var(--font-size-lg); }

.markdown-content p {
  margin: var(--spacing-md) 0;
  color: var(--text-secondary);
}

.markdown-content ul,
.markdown-content ol {
  margin: var(--spacing-md) 0;
  padding-left: var(--spacing-lg);
}

.markdown-content li {
  margin: var(--spacing-xs) 0;
  color: var(--text-secondary);
}

.markdown-content blockquote {
  margin: var(--spacing-md) 0;
  padding: var(--spacing-md);
  background-color: var(--bg-quaternary);
  border-left: 4px solid var(--primary-color);
  border-radius: var(--radius-medium);
}

.markdown-content code {
  background-color: var(--bg-quaternary);
  padding: 2px 6px;
  border-radius: var(--radius-small);
  font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
  font-size: 0.9em;
  color: var(--text-primary);
}

.markdown-content pre {
  position: relative;
  margin: var(--spacing-md) 0;
  padding: var(--spacing-md);
  background-color: var(--bg-quaternary);
  border-radius: var(--radius-medium);
  overflow-x: auto;
  border: 1px solid var(--border-color);
}

.markdown-content pre code {
  background: none;
  padding: 0;
  border-radius: 0;
  font-size: var(--font-size-sm);
}

/* 代码复制按钮 */
.copy-code-btn {
  position: absolute;
  top: var(--spacing-sm);
  right: var(--spacing-sm);
  padding: var(--spacing-xs) var(--spacing-sm);
  background-color: var(--primary-color);
  color: white;
  border: none;
  border-radius: var(--radius-small);
  font-size: var(--font-size-xs);
  cursor: pointer;
  opacity: 0;
  transition: all var(--transition-fast);
}

.markdown-content pre:hover .copy-code-btn {
  opacity: 1;
}

.copy-code-btn:hover {
  background-color: #0056CC;
  transform: scale(1.05);
}

/* 占位符样式 */
.placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: var(--text-tertiary);
  text-align: center;
}

.placeholder-icon {
  font-size: 48px;
  margin-bottom: var(--spacing-md);
  opacity: 0.5;
}

/* 反馈输入容器 */
.feedback-input-container {
  background-color: var(--bg-tertiary);
  border-radius: var(--radius-large);
  border: 1px solid var(--border-color);
  padding: var(--spacing-lg);
  box-shadow: var(--shadow-light);
}

.input-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-md);
}

.input-header h3 {
  font-size: var(--font-size-lg);
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
}

/* 保存切换开关 */
.save-toggle {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  cursor: pointer;
  user-select: none;
}

.save-toggle input[type="checkbox"] {
  display: none;
}

.toggle-slider {
  position: relative;
  width: 44px;
  height: 24px;
  background-color: var(--bg-quaternary);
  border-radius: 12px;
  transition: all var(--transition-medium);
  border: 1px solid var(--border-color);
}

.toggle-slider::before {
  content: '';
  position: absolute;
  top: 2px;
  left: 2px;
  width: 18px;
  height: 18px;
  background-color: white;
  border-radius: 50%;
  transition: all var(--transition-medium);
  box-shadow: var(--shadow-light);
}

.save-toggle input[type="checkbox"]:checked + .toggle-slider {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
}

.save-toggle input[type="checkbox"]:checked + .toggle-slider::before {
  transform: translateX(20px);
}

.toggle-text {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
}

/* 输入区域 */
.input-area {
  margin-bottom: var(--spacing-lg);
}

.input-area textarea {
  width: 100%;
  min-height: 120px;
  resize: vertical;
  font-family: var(--font-family);
}

/* 评分组件 */
.rating-container {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  margin-top: var(--spacing-md);
}

.rating-label {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
}

.rating-stars {
  display: flex;
  gap: var(--spacing-xs);
}

.star {
  font-size: var(--font-size-lg);
  cursor: pointer;
  transition: all var(--transition-fast);
  opacity: 0.3;
}

.star:hover,
.star.active {
  opacity: 1;
  transform: scale(1.1);
}

.star.active {
  filter: drop-shadow(0 0 4px rgba(255, 193, 7, 0.6));
}

/* 操作按钮 */
.action-buttons {
  display: flex;
  gap: var(--spacing-md);
  justify-content: flex-end;
}

/* 历史记录样式 */
.history-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.history-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-lg);
}

.history-header h2 {
  font-size: var(--font-size-xl);
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
}

.history-actions {
  display: flex;
  gap: var(--spacing-sm);
}

.history-list {
  flex: 1;
  overflow-y: auto;
}

.history-item {
  background-color: var(--bg-tertiary);
  border-radius: var(--radius-medium);
  border: 1px solid var(--border-color);
  padding: var(--spacing-md);
  margin-bottom: var(--spacing-md);
  transition: all var(--transition-fast);
  cursor: pointer;
}

.history-item:hover {
  box-shadow: var(--shadow-medium);
  transform: translateY(-2px);
}

.history-item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-sm);
}

.history-item-title {
  font-weight: 600;
  color: var(--text-primary);
}

.history-item-date {
  font-size: var(--font-size-xs);
  color: var(--text-tertiary);
}

.history-item-preview {
  color: var(--text-secondary);
  font-size: var(--font-size-sm);
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* 设置样式 */
.settings-container h2 {
  font-size: var(--font-size-xl);
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--spacing-lg);
}

.settings-form {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
}

.setting-group {
  background-color: var(--bg-tertiary);
  border-radius: var(--radius-medium);
  border: 1px solid var(--border-color);
  padding: var(--spacing-lg);
}

.setting-group h3 {
  font-size: var(--font-size-md);
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--spacing-md);
}

.setting-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-sm) 0;
  border-bottom: 1px solid var(--separator-color);
}

.setting-item:last-child {
  border-bottom: none;
}

.setting-label {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
}

.setting-control {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

/* 模态框样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  visibility: hidden;
  transition: all var(--transition-medium);
  z-index: 1000;
  backdrop-filter: blur(10px);
}

.modal-overlay.active {
  opacity: 1;
  visibility: visible;
}

.modal {
  background-color: var(--bg-primary);
  border-radius: var(--radius-large);
  box-shadow: var(--shadow-heavy);
  max-width: 500px;
  width: 90%;
  max-height: 80vh;
  overflow: hidden;
  transform: scale(0.9);
  transition: all var(--transition-medium);
}

.modal-overlay.active .modal {
  transform: scale(1);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-lg);
  border-bottom: 1px solid var(--separator-color);
}

.modal-title {
  font-size: var(--font-size-lg);
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
}

.modal-close {
  width: 32px;
  height: 32px;
  border: none;
  background: none;
  font-size: var(--font-size-lg);
  color: var(--text-tertiary);
  cursor: pointer;
  border-radius: var(--radius-small);
  transition: all var(--transition-fast);
}

.modal-close:hover {
  background-color: var(--bg-quaternary);
  color: var(--text-primary);
}

.modal-body {
  padding: var(--spacing-lg);
  overflow-y: auto;
  max-height: 60vh;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: var(--spacing-sm);
  padding: var(--spacing-lg);
  border-top: 1px solid var(--separator-color);
}

/* 通知样式 */
.notification-container {
  position: fixed;
  top: var(--spacing-lg);
  right: var(--spacing-lg);
  z-index: 2000;
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.notification {
  background-color: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-medium);
  padding: var(--spacing-md);
  box-shadow: var(--shadow-heavy);
  min-width: 300px;
  transform: translateX(100%);
  transition: all var(--transition-medium);
  backdrop-filter: blur(20px);
}

.notification.show {
  transform: translateX(0);
}

.notification.success {
  border-left: 4px solid var(--success-color);
}

.notification.error {
  border-left: 4px solid var(--error-color);
}

.notification.warning {
  border-left: 4px solid var(--warning-color);
}

.notification-content {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.notification-icon {
  font-size: var(--font-size-lg);
}

.notification-text {
  flex: 1;
  font-size: var(--font-size-sm);
  color: var(--text-primary);
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--bg-quaternary);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: var(--border-color);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--text-tertiary);
}

/* 表格样式 */
.table-container {
  overflow-x: auto;
  margin: var(--spacing-md) 0;
  border-radius: var(--radius-medium);
  border: 1px solid var(--border-color);
}

.markdown-table {
  width: 100%;
  border-collapse: collapse;
  font-size: var(--font-size-sm);
}

.markdown-table th,
.markdown-table td {
  padding: var(--spacing-sm) var(--spacing-md);
  text-align: left;
  border-bottom: 1px solid var(--separator-color);
}

.markdown-table th {
  background-color: var(--bg-secondary);
  font-weight: 600;
  color: var(--text-primary);
}

.markdown-table tr:hover {
  background-color: var(--bg-quaternary);
}

/* 图片样式 */
.markdown-image {
  max-width: 100%;
  height: auto;
  border-radius: var(--radius-medium);
  box-shadow: var(--shadow-light);
  margin: var(--spacing-md) 0;
}

/* 外部链接样式 */
.external-link-icon {
  opacity: 0.6;
  font-size: 0.8em;
}

/* 验证错误样式 */
.validation-error {
  color: var(--error-color);
  font-size: var(--font-size-xs);
  margin-top: var(--spacing-xs);
  padding: var(--spacing-xs) var(--spacing-sm);
  background-color: rgba(255, 59, 48, 0.1);
  border-radius: var(--radius-small);
  border-left: 3px solid var(--error-color);
}

.input-area textarea.error {
  border-color: var(--error-color);
  box-shadow: 0 0 0 3px rgba(255, 59, 48, 0.1);
}

/* 草稿指示器 */
.draft-indicator {
  position: fixed;
  bottom: var(--spacing-lg);
  left: 50%;
  transform: translateX(-50%) translateY(100%);
  background-color: var(--bg-tertiary);
  color: var(--text-secondary);
  padding: var(--spacing-xs) var(--spacing-md);
  border-radius: var(--radius-medium);
  font-size: var(--font-size-xs);
  box-shadow: var(--shadow-medium);
  transition: all var(--transition-medium);
  z-index: 1500;
}

.draft-indicator.show {
  transform: translateX(-50%) translateY(0);
}

/* 成功动画 */
.btn.success-animation {
  background-color: var(--success-color) !important;
  transform: scale(1.05);
  transition: all var(--transition-fast);
}

/* 加载状态 */
.btn.loading {
  position: relative;
  color: transparent;
}

.btn.loading::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 16px;
  height: 16px;
  margin: -8px 0 0 -8px;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 毛玻璃效果 */
.glass-effect {
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

@media (prefers-color-scheme: dark) {
  .glass-effect {
    background: rgba(0, 0, 0, 0.8);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }
}

/* 焦点样式增强 */
.btn:focus-visible,
input:focus-visible,
textarea:focus-visible,
.nav-item:focus-visible {
  outline: 2px solid var(--primary-color);
  outline-offset: 2px;
}

/* 选择文本样式 */
::selection {
  background-color: var(--primary-color);
  color: white;
}

/* 占位符文本样式 */
::placeholder {
  color: var(--text-quaternary);
  opacity: 1;
}

/* 禁用状态样式 */
.btn:disabled,
input:disabled,
textarea:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  user-select: none;
}

/* 工具提示样式 */
[title] {
  position: relative;
}

[title]:hover::after {
  content: attr(title);
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  background-color: var(--bg-primary);
  color: var(--text-primary);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-small);
  font-size: var(--font-size-xs);
  white-space: nowrap;
  box-shadow: var(--shadow-medium);
  z-index: 1000;
  border: 1px solid var(--border-color);
}

/* 响应式调整 */
@media (max-width: 1024px) {
  .feedback-container {
    gap: var(--spacing-md);
  }

  .markdown-container {
    min-height: 300px;
  }

  .feedback-input-container {
    padding: var(--spacing-md);
  }
}

@media (max-width: 768px) {
  .action-buttons {
    flex-direction: column;
  }

  .btn {
    width: 100%;
  }

  .history-header {
    flex-direction: column;
    gap: var(--spacing-sm);
    align-items: stretch;
  }

  .history-actions {
    justify-content: stretch;
  }

  .history-actions .btn {
    flex: 1;
  }
}

/* 打印样式 */
@media print {
  .sidebar,
  .title-bar,
  .status-bar,
  .action-buttons,
  .content-actions {
    display: none;
  }

  .main-content {
    margin: 0;
  }

  .content-area {
    position: static;
    padding: 0;
  }

  .view {
    position: static;
    opacity: 1;
    visibility: visible;
  }
}
