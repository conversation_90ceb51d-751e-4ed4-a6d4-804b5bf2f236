/* 主样式文件 - 基础样式和布局 */

/* 重置样式 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

/* 根变量 */
:root {
  /* 苹果风格颜色 */
  --primary-color: #007AFF;
  --secondary-color: #5856D6;
  --success-color: #34C759;
  --warning-color: #FF9500;
  --error-color: #FF3B30;
  
  /* 背景颜色 */
  --bg-primary: #FFFFFF;
  --bg-secondary: #F2F2F7;
  --bg-tertiary: #FFFFFF;
  --bg-quaternary: #F2F2F7;
  
  /* 文本颜色 */
  --text-primary: #000000;
  --text-secondary: #3C3C43;
  --text-tertiary: #3C3C4399;
  --text-quaternary: #3C3C4360;
  
  /* 边框颜色 */
  --border-color: #C6C6C8;
  --separator-color: #C6C6C8;
  
  /* 阴影 */
  --shadow-light: 0 1px 3px rgba(0, 0, 0, 0.1);
  --shadow-medium: 0 4px 12px rgba(0, 0, 0, 0.15);
  --shadow-heavy: 0 8px 25px rgba(0, 0, 0, 0.2);
  
  /* 圆角 */
  --radius-small: 6px;
  --radius-medium: 10px;
  --radius-large: 16px;
  
  /* 间距 */
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 16px;
  --spacing-lg: 24px;
  --spacing-xl: 32px;
  
  /* 字体 */
  --font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
  --font-size-xs: 12px;
  --font-size-sm: 14px;
  --font-size-md: 16px;
  --font-size-lg: 18px;
  --font-size-xl: 24px;
  --font-size-xxl: 32px;
  
  /* 动画 */
  --transition-fast: 0.15s ease;
  --transition-medium: 0.25s ease;
  --transition-slow: 0.35s ease;
}

/* 暗色模式 */
@media (prefers-color-scheme: dark) {
  :root {
    --bg-primary: #000000;
    --bg-secondary: #1C1C1E;
    --bg-tertiary: #2C2C2E;
    --bg-quaternary: #3A3A3C;
    
    --text-primary: #FFFFFF;
    --text-secondary: #EBEBF5;
    --text-tertiary: #EBEBF599;
    --text-quaternary: #EBEBF560;
    
    --border-color: #38383A;
    --separator-color: #38383A;
  }
}

/* 基础样式 */
html {
  font-size: 16px;
  line-height: 1.5;
}

body {
  font-family: var(--font-family);
  font-size: var(--font-size-md);
  color: var(--text-primary);
  background-color: var(--bg-primary);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  overflow: hidden;
}

/* 应用容器 */
.app-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: var(--bg-primary);
}

/* 标题栏 */
.title-bar {
  background-color: var(--bg-secondary);
  border-bottom: 1px solid var(--separator-color);
  padding: var(--spacing-sm) var(--spacing-md);
  -webkit-app-region: drag;
  user-select: none;
}

.title-bar-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.window-controls {
  display: flex;
  gap: var(--spacing-sm);
  -webkit-app-region: no-drag;
}

.control-button {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  cursor: pointer;
  transition: opacity var(--transition-fast);
}

.control-button:hover {
  opacity: 0.8;
}

.control-button.close {
  background-color: #FF5F57;
}

.control-button.minimize {
  background-color: #FFBD2E;
}

.control-button.maximize {
  background-color: #28CA42;
}

.app-title {
  font-size: var(--font-size-md);
  font-weight: 600;
  color: var(--text-primary);
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: var(--success-color);
}

/* 主内容区 */
.main-content {
  display: flex;
  flex: 1;
  overflow: hidden;
}

/* 侧边栏 */
.sidebar {
  width: 200px;
  background-color: var(--bg-secondary);
  border-right: 1px solid var(--separator-color);
  padding: var(--spacing-md);
}

.sidebar-nav {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.nav-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-sm) var(--spacing-md);
  border: none;
  background: none;
  color: var(--text-secondary);
  font-size: var(--font-size-sm);
  border-radius: var(--radius-medium);
  cursor: pointer;
  transition: all var(--transition-fast);
  text-align: left;
  width: 100%;
}

.nav-item:hover {
  background-color: var(--bg-tertiary);
  color: var(--text-primary);
}

.nav-item.active {
  background-color: var(--primary-color);
  color: white;
}

.nav-icon {
  font-size: var(--font-size-md);
}

/* 内容区域 */
.content-area {
  flex: 1;
  overflow: hidden;
  position: relative;
}

.view {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  opacity: 0;
  visibility: hidden;
  transition: all var(--transition-medium);
  overflow-y: auto;
  padding: var(--spacing-lg);
}

.view.active {
  opacity: 1;
  visibility: visible;
}

/* 按钮样式 */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-xs);
  padding: var(--spacing-sm) var(--spacing-md);
  border: none;
  border-radius: var(--radius-medium);
  font-size: var(--font-size-sm);
  font-weight: 500;
  cursor: pointer;
  transition: all var(--transition-fast);
  text-decoration: none;
  user-select: none;
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.btn-primary {
  background-color: var(--primary-color);
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background-color: #0056CC;
  transform: translateY(-1px);
  box-shadow: var(--shadow-medium);
}

.btn-secondary {
  background-color: var(--bg-tertiary);
  color: var(--text-primary);
  border: 1px solid var(--border-color);
}

.btn-secondary:hover:not(:disabled) {
  background-color: var(--bg-quaternary);
  transform: translateY(-1px);
  box-shadow: var(--shadow-light);
}

/* 输入框样式 */
input, textarea {
  font-family: var(--font-family);
  font-size: var(--font-size-md);
  color: var(--text-primary);
  background-color: var(--bg-tertiary);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-medium);
  padding: var(--spacing-sm) var(--spacing-md);
  transition: all var(--transition-fast);
  outline: none;
}

input:focus, textarea:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(0, 122, 255, 0.1);
}

textarea {
  resize: vertical;
  min-height: 100px;
}

/* 状态栏 */
.status-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-xs) var(--spacing-md);
  background-color: var(--bg-secondary);
  border-top: 1px solid var(--separator-color);
  font-size: var(--font-size-xs);
  color: var(--text-tertiary);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .sidebar {
    width: 60px;
  }
  
  .nav-text {
    display: none;
  }
  
  .view {
    padding: var(--spacing-md);
  }
}
