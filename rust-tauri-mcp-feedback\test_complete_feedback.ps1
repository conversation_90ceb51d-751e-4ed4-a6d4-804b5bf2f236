# 完整的MCP Feedback Tool测试脚本
$ErrorActionPreference = "Stop"

Write-Host "=== MCP Feedback Tool 完整功能测试 ===" -ForegroundColor Green

$ExePath = ".\src-tauri\target\release\mcp_server.exe"

if (-not (Test-Path $ExePath)) {
    Write-Host "错误: 可执行文件不存在，正在构建..." -ForegroundColor Yellow
    Set-Location "src-tauri"
    cargo build --release --bin mcp_server
    Set-Location ".."
}

Write-Host "使用可执行文件: $ExePath" -ForegroundColor Cyan

# 测试1: 初始化MCP服务器
Write-Host "`n=== 测试1: 初始化MCP服务器 ===" -ForegroundColor Yellow
$initRequest = '{"jsonrpc":"2.0","id":1,"method":"initialize","params":{"protocolVersion":"2024-11-05","capabilities":{},"clientInfo":{"name":"test-client","version":"1.0.0"}}}'
Write-Host "发送初始化请求..." -ForegroundColor Gray
$initResponse = $initRequest | & $ExePath
Write-Host "初始化响应: $initResponse" -ForegroundColor Green

# 解析响应检查是否成功
$initResult = $initResponse | ConvertFrom-Json
if ($initResult.error) {
    Write-Host "初始化失败: $($initResult.error.message)" -ForegroundColor Red
    exit 1
}
Write-Host "✅ 初始化成功" -ForegroundColor Green

# 测试2: 获取工具列表
Write-Host "`n=== 测试2: 获取工具列表 ===" -ForegroundColor Yellow
$toolsRequest = '{"jsonrpc":"2.0","id":2,"method":"tools/list","params":{}}'
Write-Host "发送工具列表请求..." -ForegroundColor Gray
$toolsResponse = $toolsRequest | & $ExePath
Write-Host "工具列表响应: $toolsResponse" -ForegroundColor Green

$toolsResult = $toolsResponse | ConvertFrom-Json
if ($toolsResult.error) {
    Write-Host "获取工具列表失败: $($toolsResult.error.message)" -ForegroundColor Red
    exit 1
}

$feedbackTool = $toolsResult.result.tools | Where-Object { $_.name -eq "feedback" }
if (-not $feedbackTool) {
    Write-Host "未找到feedback工具" -ForegroundColor Red
    exit 1
}
Write-Host "✅ 找到feedback工具: $($feedbackTool.description)" -ForegroundColor Green

# 测试3: 调用feedback工具 - 简单测试
Write-Host "`n=== 测试3: 调用feedback工具 (简单测试) ===" -ForegroundColor Yellow
$simpleCallRequest = @{
    jsonrpc = "2.0"
    id = 3
    method = "tools/call"
    params = @{
        name = "feedback"
        arguments = @{
            work_summary = "# Simple Test`n`nThis is a simple test of the feedback tool.`n`n## Features`n`n- Basic functionality`n- Simple content"
            title = "Simple Test"
            allow_save = $true
        }
    }
} | ConvertTo-Json -Depth 10 -Compress

Write-Host "发送简单工具调用请求..." -ForegroundColor Gray
$simpleCallResponse = $simpleCallRequest | & $ExePath
Write-Host "简单调用响应: $simpleCallResponse" -ForegroundColor Green

$simpleResult = $simpleCallResponse | ConvertFrom-Json
if ($simpleResult.error) {
    Write-Host "简单工具调用失败: $($simpleResult.error.message)" -ForegroundColor Red
    exit 1
}
Write-Host "✅ 简单工具调用成功" -ForegroundColor Green

# 测试4: 调用feedback工具 - 复杂测试
Write-Host "`n=== 测试4: 调用feedback工具 (复杂测试) ===" -ForegroundColor Yellow
$complexCallRequest = @{
    jsonrpc = "2.0"
    id = 4
    method = "tools/call"
    params = @{
        name = "feedback"
        arguments = @{
            work_summary = @"
# MCP Feedback Tool 功能测试

这是一个**复杂的测试**，用来验证MCP反馈工具的各项功能。

## 主要功能

### 1. Markdown渲染
- **粗体文本**
- *斜体文本*
- `代码片段`

### 2. 代码高亮
```javascript
function testFunction() {
    console.log('Hello, MCP Feedback Tool!');
    return 'success';
}
```

```python
def test_function():
    print("Hello from Python!")
    return True
```

### 3. 列表功能
1. 有序列表项1
2. 有序列表项2
3. 有序列表项3

- 无序列表项A
- 无序列表项B
- 无序列表项C

### 4. 链接和图片
[GitHub](https://github.com)

### 5. 表格
| 功能 | 状态 | 备注 |
|------|------|------|
| Markdown渲染 | ✅ | 完成 |
| 代码高亮 | ✅ | 完成 |
| 用户反馈 | 🔄 | 测试中 |

## 测试结果

如果您能看到这个内容并且格式正确，说明Markdown渲染功能正常工作。

请在下方提供您的反馈意见！
"@
            title = "复杂功能测试"
            timeout = 600
            allow_save = $true
        }
    }
} | ConvertTo-Json -Depth 10 -Compress

Write-Host "发送复杂工具调用请求..." -ForegroundColor Gray
$complexCallResponse = $complexCallRequest | & $ExePath
Write-Host "复杂调用响应: $complexCallResponse" -ForegroundColor Green

$complexResult = $complexCallResponse | ConvertFrom-Json
if ($complexResult.error) {
    Write-Host "复杂工具调用失败: $($complexResult.error.message)" -ForegroundColor Red
    exit 1
}
Write-Host "✅ 复杂工具调用成功" -ForegroundColor Green

Write-Host "`n=== 测试总结 ===" -ForegroundColor Green
Write-Host "✅ MCP服务器初始化 - 成功" -ForegroundColor Green
Write-Host "✅ 工具列表获取 - 成功" -ForegroundColor Green
Write-Host "✅ 简单工具调用 - 成功" -ForegroundColor Green
Write-Host "✅ 复杂工具调用 - 成功" -ForegroundColor Green
Write-Host "`n🎉 所有MCP服务器测试通过！" -ForegroundColor Green

Write-Host "`n📝 注意事项:" -ForegroundColor Yellow
Write-Host "- MCP服务器功能正常" -ForegroundColor White
Write-Host "- 前端UI需要手动测试" -ForegroundColor White
Write-Host "- 数据库存储功能需要验证" -ForegroundColor White
Write-Host "- 用户反馈收集需要交互测试" -ForegroundColor White
