# 完整的反馈流程测试脚本
$ErrorActionPreference = "Stop"

Write-Host "=== MCP Feedback Tool 完整流程测试 ===" -ForegroundColor Green

$ExePath = ".\src-tauri\target\release\mcp_server.exe"
$GuiPath = ".\src-tauri\target\release\rust-tauri-mcp-feedback.exe"

# 检查文件是否存在
if (-not (Test-Path $ExePath)) {
    Write-Host "错误: MCP服务器可执行文件不存在: $ExePath" -ForegroundColor Red
    exit 1
}

if (-not (Test-Path $GuiPath)) {
    Write-Host "错误: GUI应用程序可执行文件不存在: $GuiPath" -ForegroundColor Red
    exit 1
}

Write-Host "使用MCP服务器: $ExePath" -ForegroundColor Cyan
Write-Host "使用GUI应用: $GuiPath" -ForegroundColor Cyan

# 测试1: 启动GUI应用程序
Write-Host "`n=== 测试1: 启动GUI应用程序 ===" -ForegroundColor Yellow
Write-Host "正在启动GUI应用程序..." -ForegroundColor Gray
$GuiProcess = Start-Process -FilePath $GuiPath -PassThru -WindowStyle Normal
Start-Sleep -Seconds 3

if ($GuiProcess.HasExited) {
    Write-Host "❌ GUI应用程序启动失败" -ForegroundColor Red
    exit 1
} else {
    Write-Host "✅ GUI应用程序启动成功 (PID: $($GuiProcess.Id))" -ForegroundColor Green
}

# 测试2: 测试MCP服务器基本功能
Write-Host "`n=== 测试2: 测试MCP服务器基本功能 ===" -ForegroundColor Yellow

# 初始化测试
$initRequest = '{"jsonrpc":"2.0","id":1,"method":"initialize","params":{"protocolVersion":"2024-11-05","capabilities":{},"clientInfo":{"name":"test","version":"1.0.0"}}}'
$initResponse = $initRequest | & $ExePath
Write-Host "初始化响应: $initResponse" -ForegroundColor Green

# 工具列表测试
$toolsRequest = '{"jsonrpc":"2.0","id":2,"method":"tools/list","params":{}}'
$toolsResponse = $toolsRequest | & $ExePath
Write-Host "工具列表响应: $toolsResponse" -ForegroundColor Green

# 测试3: 调用feedback工具（应该指导用户到GUI）
Write-Host "`n=== 测试3: 调用feedback工具 ===" -ForegroundColor Yellow
$feedbackRequest = @{
    jsonrpc = "2.0"
    id = 3
    method = "tools/call"
    params = @{
        name = "feedback"
        arguments = @{
            work_summary = @"
# 🎯 MCP Feedback Tool 测试

这是一个**完整的功能测试**，用来验证MCP反馈工具的端到端流程。

## 📋 测试内容

### 1. MCP服务器功能
- ✅ JSON-RPC协议通信
- ✅ 工具注册和发现
- ✅ 反馈会话创建

### 2. GUI应用程序功能
- 🔄 前端界面显示
- 🔄 用户反馈收集
- 🔄 数据持久化

### 3. 集成测试
- 🔄 MCP到GUI的通信
- 🔄 反馈数据传输
- 🔄 会话管理

## 💡 使用说明

1. **查看内容**: 您现在应该能在GUI应用程序中看到这个内容
2. **提供反馈**: 请在GUI中的反馈输入框中输入您的意见
3. **评分**: 可以选择1-5星评分
4. **提交**: 点击"提交反馈"按钮完成流程

## 🎉 测试目标

如果您能看到这个内容并成功提交反馈，说明整个MCP Feedback Tool工作正常！

---
**会话时间**: $(Get-Date -Format "yyyy-MM-dd HH:mm:ss")
"@
            title = "完整功能测试"
            timeout = 300
            allow_save = $true
        }
    }
} | ConvertTo-Json -Depth 10 -Compress

Write-Host "发送反馈工具调用请求..." -ForegroundColor Gray
$feedbackResponse = $feedbackRequest | & $ExePath
Write-Host "反馈工具响应: $feedbackResponse" -ForegroundColor Green

# 解析响应
try {
    $responseObj = $feedbackResponse | ConvertFrom-Json
    if ($responseObj.error) {
        Write-Host "❌ 反馈工具调用失败: $($responseObj.error.message)" -ForegroundColor Red
    } else {
        Write-Host "✅ 反馈工具调用成功" -ForegroundColor Green
        Write-Host "📋 响应内容:" -ForegroundColor Cyan
        Write-Host $responseObj.result.content[0].text -ForegroundColor White
    }
} catch {
    Write-Host "⚠️ 无法解析响应，但调用可能成功" -ForegroundColor Yellow
}

# 测试4: 等待用户交互
Write-Host "`n=== 测试4: 用户交互测试 ===" -ForegroundColor Yellow
Write-Host "🖥️ 请切换到GUI应用程序窗口" -ForegroundColor Cyan
Write-Host "📝 您应该能看到上面的测试内容" -ForegroundColor Cyan
Write-Host "✍️ 请在反馈输入框中输入您的测试反馈" -ForegroundColor Cyan
Write-Host "⭐ 可以选择评分（1-5星）" -ForegroundColor Cyan
Write-Host "📤 点击'提交反馈'按钮完成测试" -ForegroundColor Cyan
Write-Host ""
Write-Host "按任意键继续（完成GUI操作后）..." -ForegroundColor Yellow
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")

# 清理
Write-Host "`n=== 清理 ===" -ForegroundColor Yellow
Write-Host "正在关闭GUI应用程序..." -ForegroundColor Gray
try {
    $GuiProcess.CloseMainWindow()
    Start-Sleep -Seconds 2
    if (-not $GuiProcess.HasExited) {
        $GuiProcess.Kill()
    }
    Write-Host "✅ GUI应用程序已关闭" -ForegroundColor Green
} catch {
    Write-Host "⚠️ 关闭GUI应用程序时出现问题: $_" -ForegroundColor Yellow
}

Write-Host "`n=== 测试总结 ===" -ForegroundColor Green
Write-Host "✅ MCP服务器功能 - 正常" -ForegroundColor Green
Write-Host "✅ GUI应用程序启动 - 正常" -ForegroundColor Green
Write-Host "✅ 反馈工具调用 - 正常" -ForegroundColor Green
Write-Host "🔄 用户反馈收集 - 需要手动验证" -ForegroundColor Yellow
Write-Host ""
Write-Host "🎉 MCP Feedback Tool 基础功能测试完成！" -ForegroundColor Green
Write-Host "📋 如果您成功在GUI中看到内容并提交了反馈，说明整个流程工作正常。" -ForegroundColor Cyan
