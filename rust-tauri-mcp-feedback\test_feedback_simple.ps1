# Simple Feedback Flow Test
$ErrorActionPreference = "Stop"

Write-Host "=== MCP Feedback Tool Test ===" -ForegroundColor Green

$ExePath = ".\src-tauri\target\release\mcp_server.exe"
$GuiPath = ".\src-tauri\target\release\rust-tauri-mcp-feedback.exe"

if (-not (Test-Path $ExePath)) {
    Write-Host "Error: MCP server not found: $ExePath" -ForegroundColor Red
    exit 1
}

if (-not (Test-Path $GuiPath)) {
    Write-Host "Error: GUI app not found: $GuiPath" -ForegroundColor Red
    exit 1
}

Write-Host "MCP Server: $ExePath" -ForegroundColor Cyan
Write-Host "GUI App: $GuiPath" -ForegroundColor Cyan

# Test 1: Start GUI
Write-Host "`n=== Test 1: Start GUI ===" -ForegroundColor Yellow
$GuiProcess = Start-Process -FilePath $GuiPath -PassThru -WindowStyle Normal
Start-Sleep -Seconds 3

if ($GuiProcess.HasExited) {
    Write-Host "GUI failed to start" -ForegroundColor Red
    exit 1
} else {
    Write-Host "GUI started successfully (PID: $($GuiProcess.Id))" -ForegroundColor Green
}

# Test 2: Test MCP Server
Write-Host "`n=== Test 2: Test MCP Server ===" -ForegroundColor Yellow

$initRequest = '{"jsonrpc":"2.0","id":1,"method":"initialize","params":{"protocolVersion":"2024-11-05","capabilities":{},"clientInfo":{"name":"test","version":"1.0.0"}}}'
$initResponse = $initRequest | & $ExePath
Write-Host "Init response: OK" -ForegroundColor Green

$toolsRequest = '{"jsonrpc":"2.0","id":2,"method":"tools/list","params":{}}'
$toolsResponse = $toolsRequest | & $ExePath
Write-Host "Tools response: OK" -ForegroundColor Green

# Test 3: Call feedback tool
Write-Host "`n=== Test 3: Call Feedback Tool ===" -ForegroundColor Yellow
$feedbackRequest = '{"jsonrpc":"2.0","id":3,"method":"tools/call","params":{"name":"feedback","arguments":{"work_summary":"# Test Feedback\n\nThis is a test message for the feedback tool.\n\n## Instructions\n\n1. You should see this content in the GUI\n2. Please provide feedback in the input box\n3. Click Submit Feedback\n\nThank you for testing!","title":"Test Session","timeout":300,"allow_save":true}}}'
$feedbackResponse = $feedbackRequest | & $ExePath
Write-Host "Feedback tool called successfully" -ForegroundColor Green

# Test 4: User interaction
Write-Host "`n=== Test 4: User Interaction ===" -ForegroundColor Yellow
Write-Host "Please switch to the GUI application window" -ForegroundColor Cyan
Write-Host "You should see the test content displayed" -ForegroundColor Cyan
Write-Host "Please provide feedback and click Submit" -ForegroundColor Cyan
Write-Host ""
Write-Host "Press any key to continue (after completing GUI interaction)..." -ForegroundColor Yellow
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")

# Cleanup
Write-Host "`n=== Cleanup ===" -ForegroundColor Yellow
try {
    $GuiProcess.CloseMainWindow()
    Start-Sleep -Seconds 2
    if (-not $GuiProcess.HasExited) {
        $GuiProcess.Kill()
    }
    Write-Host "GUI application closed" -ForegroundColor Green
} catch {
    Write-Host "Warning: Issue closing GUI: $_" -ForegroundColor Yellow
}

Write-Host "`n=== Test Summary ===" -ForegroundColor Green
Write-Host "MCP Server: OK" -ForegroundColor Green
Write-Host "GUI Application: OK" -ForegroundColor Green
Write-Host "Feedback Tool: OK" -ForegroundColor Green
Write-Host "User Interaction: Manual verification required" -ForegroundColor Yellow
Write-Host ""
Write-Host "Test completed successfully!" -ForegroundColor Green
