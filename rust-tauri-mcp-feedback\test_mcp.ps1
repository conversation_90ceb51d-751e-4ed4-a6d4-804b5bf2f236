# 测试MCP服务器功能
$ErrorActionPreference = "Stop"

Write-Host "测试MCP Feedback Tool服务器..." -ForegroundColor Green

$ExePath = ".\src-tauri\target\release\mcp_server.exe"

if (-not (Test-Path $ExePath)) {
    Write-Host "错误: 可执行文件不存在: $ExePath" -ForegroundColor Red
    exit 1
}

Write-Host "使用可执行文件: $ExePath" -ForegroundColor Cyan

# 测试1: 初始化
Write-Host "`n=== 测试1: 初始化 ===" -ForegroundColor Yellow
$initRequest = '{"jsonrpc":"2.0","id":1,"method":"initialize","params":{"protocolVersion":"2024-11-05","capabilities":{},"clientInfo":{"name":"test","version":"1.0.0"}}}'
Write-Host "发送: $initRequest" -ForegroundColor Gray
$initResponse = $initRequest | & $ExePath
Write-Host "响应: $initResponse" -ForegroundColor Green

# 测试2: 工具列表
Write-Host "`n=== 测试2: 工具列表 ===" -ForegroundColor Yellow
$toolsRequest = '{"jsonrpc":"2.0","id":2,"method":"tools/list","params":{}}'
Write-Host "发送: $toolsRequest" -ForegroundColor Gray
$toolsResponse = $toolsRequest | & $ExePath
Write-Host "响应: $toolsResponse" -ForegroundColor Green

# 测试3: 工具调用
Write-Host "`n=== 测试3: 工具调用 ===" -ForegroundColor Yellow
$callRequest = '{"jsonrpc":"2.0","id":3,"method":"tools/call","params":{"name":"feedback","arguments":{"work_summary":"# 测试反馈工具\n\n这是一个测试消息。\n\n## 功能测试\n\n- Markdown渲染\n- 用户反馈收集","title":"测试会话"}}}'
Write-Host "发送: $callRequest" -ForegroundColor Gray
$callResponse = $callRequest | & $ExePath
Write-Host "响应: $callResponse" -ForegroundColor Green

Write-Host "`n测试完成!" -ForegroundColor Green
