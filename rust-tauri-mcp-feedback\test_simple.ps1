# Simple MCP Feedback Tool Test
$ErrorActionPreference = "Stop"

Write-Host "=== MCP Feedback Tool Test ===" -ForegroundColor Green

$ExePath = ".\src-tauri\target\release\mcp_server.exe"

if (-not (Test-Path $ExePath)) {
    Write-Host "Building mcp_server..." -ForegroundColor Yellow
    Set-Location "src-tauri"
    cargo build --release --bin mcp_server
    Set-Location ".."
}

Write-Host "Using executable: $ExePath" -ForegroundColor Cyan

# Test 1: Initialize
Write-Host "`n=== Test 1: Initialize ===" -ForegroundColor Yellow
$initRequest = '{"jsonrpc":"2.0","id":1,"method":"initialize","params":{"protocolVersion":"2024-11-05","capabilities":{},"clientInfo":{"name":"test","version":"1.0.0"}}}'
$initResponse = $initRequest | & $ExePath
Write-Host "Init response: $initResponse" -ForegroundColor Green

# Test 2: List tools
Write-Host "`n=== Test 2: List Tools ===" -ForegroundColor Yellow
$toolsRequest = '{"jsonrpc":"2.0","id":2,"method":"tools/list","params":{}}'
$toolsResponse = $toolsRequest | & $ExePath
Write-Host "Tools response: $toolsResponse" -ForegroundColor Green

# Test 3: Call feedback tool
Write-Host "`n=== Test 3: Call Feedback Tool ===" -ForegroundColor Yellow
$callRequest = '{"jsonrpc":"2.0","id":3,"method":"tools/call","params":{"name":"feedback","arguments":{"work_summary":"# Test Feedback\n\nThis is a test message.\n\n## Features\n\n- Markdown rendering\n- User feedback collection","title":"Test Session"}}}'
$callResponse = $callRequest | & $ExePath
Write-Host "Call response: $callResponse" -ForegroundColor Green

Write-Host "`n=== Test Complete ===" -ForegroundColor Green
